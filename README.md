1. <PERSON>lone the repo

2. Configure local env vars
   `cp .env.dist .env`

3. Install deps
   `npm install`

4. Start docker compose
   `docker compose up -d`

5 Setup local stack (run these commands on your host machine)

```
aws --endpoint-url=http://localhost:4566 --region=us-east-1 s3 mb s3://letzai-images
aws --endpoint-url=http://localhost:4566 --region=us-east-1 s3 mb s3://letzai-models
aws --endpoint-url=http://localhost:4566 --region=us-east-1 s3 mb s3://letzai-videos

aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_v1
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_v2
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_v3
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_fast
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_vip
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name models_v1
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name models_v2
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name models_v3
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name video
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name upscale
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name image_resizer
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name image_edit
```
