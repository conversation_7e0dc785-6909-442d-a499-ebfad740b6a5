import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1754420741339 implements MigrationInterface {
  name = 'Api1754420741339';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "video" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" uuid NOT NULL,
                "organization_id" uuid,
                "original_image_completion_id" uuid,
                "image_url" text,
                "prompt" text,
                "prompt_system" text,
                "storage_bucket" text,
                "storage_path" text,
                "resolution" integer,
                "width" integer NOT NULL DEFAULT '480',
                "height" integer NOT NULL DEFAULT '480',
                "settings" json,
                "webhook_url" text,
                "status" character varying NOT NULL DEFAULT 'new',
                "progress" integer NOT NULL DEFAULT '0',
                "is_nsfw" boolean NOT NULL DEFAULT false,
                "is_hot" boolean NOT NULL DEFAULT false,
                "is_unsafe" boolean NOT NULL DEFAULT false,
                "generation_seconds" integer NOT NULL DEFAULT '0',
                "hide_prompt" boolean NOT NULL DEFAULT false,
                "blocked_at" TIMESTAMP,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_1a2f3856250765d72e7e1636c8e" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_user_id" ON "video" ("user_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_organization_id" ON "video" ("organization_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_image_completion_id" ON "video" ("original_image_completion_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_deleted_at" ON "video" ("deleted_at")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_created_at" ON "video" ("created_at")
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD CONSTRAINT "FK_0c06b8d2494611b35c67296356c" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD CONSTRAINT "FK_1386414a3fde372da8bacdbe9c2" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD CONSTRAINT "FK_14b175a55c4c5620c61eb116a72" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "video" DROP CONSTRAINT "FK_14b175a55c4c5620c61eb116a72"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP CONSTRAINT "FK_1386414a3fde372da8bacdbe9c2"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP CONSTRAINT "FK_0c06b8d2494611b35c67296356c"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_created_at"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_deleted_at"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_image_completion_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_organization_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_user_id"
        `);
    await queryRunner.query(`
            DROP TABLE "video"
        `);
  }
}
