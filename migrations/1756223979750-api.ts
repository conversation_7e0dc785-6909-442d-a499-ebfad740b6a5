import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1756223979750 implements MigrationInterface {
  name = 'Api1756223979750';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "video_image_completion" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "video_id" uuid NOT NULL,
                "image_completion_id" uuid NOT NULL,
                "is_saved" boolean NOT NULL DEFAULT false,
                CONSTRAINT "REL_1f9f4081d5da0e7d61ac9c2133" UNIQUE ("image_completion_id"),
                CONSTRAINT "PK_000349dadc99d33e6f7010dafb0" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_image_completion_video_id" ON "video_image_completion" ("video_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "progress"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "is_nsfw"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "is_hot"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "is_unsafe"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "blocked_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "likes"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "comments"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "privacy"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "image_url"
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "generated_image_completion_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "input_image_url" text
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "system_version" integer NOT NULL DEFAULT '1'
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "image_completions_count" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP CONSTRAINT "FK_14b175a55c4c5620c61eb116a72"
        `);
    await queryRunner.query(`
                ALTER TABLE "video"
                ALTER COLUMN "resolution"
                SET DEFAULT '480'
            `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_original_image_completion_id" ON "video" ("original_image_completion_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_generated_image_completion_id" ON "video" ("generated_image_completion_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD CONSTRAINT "FK_14b175a55c4c5620c61eb116a72" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD CONSTRAINT "FK_d0dc7073c5af43a82d6455a0ead" FOREIGN KEY ("generated_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_image_completion"
            ADD CONSTRAINT "FK_76839e9ae6454ec644967226ddb" FOREIGN KEY ("video_id") REFERENCES "video"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_image_completion"
            ADD CONSTRAINT "FK_1f9f4081d5da0e7d61ac9c21335" FOREIGN KEY ("image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "video_image_completion" DROP CONSTRAINT "FK_1f9f4081d5da0e7d61ac9c21335"
        `);
    await queryRunner.query(`
            ALTER TABLE "video_image_completion" DROP CONSTRAINT "FK_76839e9ae6454ec644967226ddb"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP CONSTRAINT "FK_d0dc7073c5af43a82d6455a0ead"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP CONSTRAINT "FK_14b175a55c4c5620c61eb116a72"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_generated_image_completion_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_original_image_completion_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ALTER COLUMN "resolution" DROP DEFAULT
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ALTER COLUMN "original_image_completion_id" DROP NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD CONSTRAINT "FK_14b175a55c4c5620c61eb116a72" FOREIGN KEY ("original_image_completion_id") REFERENCES "image_completion"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "image_completions_count"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "system_version"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "input_image_url"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "generated_image_completion_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "image_url" text
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "privacy" text
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "comments" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "likes" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "blocked_at" TIMESTAMP
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "is_unsafe" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "is_hot" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "is_nsfw" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "progress" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_image_completion_video_id"
        `);
    await queryRunner.query(`
            DROP TABLE "video_image_completion"
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_image_completion_id" ON "video" ("original_image_completion_id")
        `);
  }
}
