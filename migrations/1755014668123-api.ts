import { MigrationInterface, QueryRunner } from 'typeorm';

export class Api1755014668123 implements MigrationInterface {
  name = 'Api1755014668123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "video_comment" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "video_id" uuid NOT NULL,
                "user_id" uuid NOT NULL,
                "comment" text NOT NULL,
                "likes" integer NOT NULL DEFAULT '0',
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_575ab1a548728c0305b1a68b7dd" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_comment_video_id" ON "video_comment" ("video_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_comment_user_id" ON "video_comment" ("user_id")
        `);
    await queryRunner.query(`
            CREATE TABLE "video_comment_like" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "video_comment_id" uuid NOT NULL,
                "user_id" uuid NOT NULL,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_1abb6fe8b691b919484e215eaca" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_comment_like_video_comment_id" ON "video_comment_like" ("video_comment_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_comment_like_user_id" ON "video_comment_like" ("user_id")
        `);
    await queryRunner.query(`
            CREATE TABLE "video_like" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "video_id" uuid NOT NULL,
                "user_id" uuid NOT NULL,
                "deleted_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_27c16de84a29036dbd02ae387ea" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_like_video_id" ON "video_like" ("video_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "idx_video_like_user_id" ON "video_like" ("user_id")
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "privacy" text
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "video_paths" json
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "likes" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "video"
            ADD "comments" integer NOT NULL DEFAULT '0'
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment"
            ADD CONSTRAINT "FK_a3c762ba8ad4ec0dee7bb2a358e" FOREIGN KEY ("video_id") REFERENCES "video"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment"
            ADD CONSTRAINT "FK_e9cc8287e023f8b7196fd654c37" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment_like"
            ADD CONSTRAINT "FK_92ac9693289051abdd222e554dd" FOREIGN KEY ("video_comment_id") REFERENCES "video_comment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment_like"
            ADD CONSTRAINT "FK_0bf0cff367052c7b57830a0ade0" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_like"
            ADD CONSTRAINT "FK_bd4a635385de23fae56ecfeb25b" FOREIGN KEY ("video_id") REFERENCES "video"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "video_like"
            ADD CONSTRAINT "FK_875e447e7d8e392b9a58a9ee1a0" FOREIGN KEY ("user_id") REFERENCES "user_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "video_like" DROP CONSTRAINT "FK_875e447e7d8e392b9a58a9ee1a0"
        `);
    await queryRunner.query(`
            ALTER TABLE "video_like" DROP CONSTRAINT "FK_bd4a635385de23fae56ecfeb25b"
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment_like" DROP CONSTRAINT "FK_0bf0cff367052c7b57830a0ade0"
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment_like" DROP CONSTRAINT "FK_92ac9693289051abdd222e554dd"
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment" DROP CONSTRAINT "FK_e9cc8287e023f8b7196fd654c37"
        `);
    await queryRunner.query(`
            ALTER TABLE "video_comment" DROP CONSTRAINT "FK_a3c762ba8ad4ec0dee7bb2a358e"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "comments"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "likes"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "video_paths"
        `);
    await queryRunner.query(`
            ALTER TABLE "video" DROP COLUMN "privacy"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_like_user_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_like_video_id"
        `);
    await queryRunner.query(`
            DROP TABLE "video_like"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_comment_like_user_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_comment_like_video_comment_id"
        `);
    await queryRunner.query(`
            DROP TABLE "video_comment_like"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_comment_user_id"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."idx_video_comment_video_id"
        `);
    await queryRunner.query(`
            DROP TABLE "video_comment"
        `);
  }
}
