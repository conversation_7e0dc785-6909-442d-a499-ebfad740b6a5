# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

## Architecture Overview

This is a **LetzAI API** - a NestJS-based backend service that provides an AI-powered image and model generation platform. The application follows a modular microservice-oriented architecture with asynchronous queue processing.

### Core Architecture Components

- **NestJS Framework**: Modern Node.js framework with TypeScript, dependency injection, and decorator-based architecture
- **Database Layer**: PostgreSQL with TypeORM ORM for data persistence
- **Queue System**: AWS SQS for asynchronous processing of image/model generation tasks
- **Storage**: AWS S3 for file storage (images, models, assets)
- **Authentication**: JWT-based auth with support for OAuth (Facebook, Google, Apple) and SAML
- **API Design**: RESTful APIs with Swagger documentation
- **Logging**: Pino logger with Elasticsearch/Kibana integration
- **Monitoring**: Sentry for error tracking and performance monitoring

### Dual API Architecture

The application can operate in two modes via environment variables:
- **Private API** (`CONTROLLERS_ENABLE_PRIVATE=true`): Internal business logic, user management, admin features
- **Public API** (`CONTROLLERS_ENABLE_PUBLIC=true`): Public-facing endpoints for integrations

### Key Business Domains

1. **User Management**: Authentication, profiles, organizations, subscriptions
2. **AI Models**: Custom model training, versioning, privacy controls
3. **Image Generation**: Text-to-image generation with queuing system
4. **Image Editing**: AI-powered image editing capabilities
5. **Video Processing**: Video generation and processing
6. **Subscription System**: Credit-based billing with Stripe integration
7. **Social Features**: Boards, following, sharing, feeds

## Development Commands

### Environment Setup
```bash
# Copy environment template
cp .env.dist .env

# Install dependencies
npm install

# Start development environment
docker compose up -d

# Setup LocalStack (AWS services emulation)
# Run these AWS CLI commands on your host machine:
aws --endpoint-url=http://localhost:4566 --region=us-east-1 s3 mb s3://letzai-images
aws --endpoint-url=http://localhost:4566 --region=us-east-1 s3 mb s3://letzai-models
aws --endpoint-url=http://localhost:4566 --region=us-east-1 s3 mb s3://letzai-videos

aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_v1
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_v2
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_v3
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_fast
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name images_vip
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name models_v1
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name models_v2
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name models_v3
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name video
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name upscale
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name image_resizer
aws --endpoint-url=http://localhost:4566 sqs create-queue --region=us-east-1 --no-cli-pager --queue-name image_edit
```

### Build & Run
```bash
# Development with hot reload
npm run start:dev

# Debug mode
npm run start:debug

# Production build
npm run build

# Production start
npm run start:prod
```

### Testing
```bash
# Run all tests
npm run test

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:cov

# E2E tests
npm run test:e2e

# Debug tests
npm run test:debug
```

### Database Management
```bash
# Run pending migrations
npm run typeorm:migration:run

# Generate new migration
npm run typeorm:migration:generate --name=MigrationName

# Create blank migration
npm run typeorm:migration:create --name=MigrationName

# Revert last migration
npm run typeorm:migration:revert
```

### Code Quality
```bash
# Lint and fix
npm run lint

# Format code
npm run format
```

## Project Structure

### Core Modules Structure
```
src/
├── auth/              # Authentication & authorization
├── user/              # User management
├── organization/      # Organization management
├── model/             # AI model management
├── image-completion/  # Image generation
├── image-edit/        # Image editing
├── video/             # Video processing
├── upscale/           # Image upscaling
├── subscription/      # Billing & credits
├── notification/      # Notification system
├── search/           # Search functionality
├── feed/             # Social feed
├── board/            # User boards/collections
├── bookmark/         # Bookmarking
├── statistics/       # Analytics
├── chat/             # Chat functionality
├── public-api/       # Public API endpoints
├── internal/         # Internal endpoints
└── core/             # Shared utilities, validation, TypeORM config
```

### Key Configuration Files
- `app.module.ts`: Main application module with conditional controller loading
- `main.ts`: Application bootstrap with Swagger setup
- `ormconfig-migrations.ts`: TypeORM configuration for migrations
- `docker-compose.yml`: Development environment with PostgreSQL, LocalStack, ELK stack

## Database Schema

### Core Entities
- **User**: User accounts with OAuth support, credits, preferences
- **Organization**: Multi-tenant organizations with subscriptions
- **Model**: AI models with versioning, privacy controls, and training status
- **ImageCompletion**: Generated images with prompts, models, and queue status
- **ImageEdit**: Image editing tasks
- **Video**: Video generation tasks
- **Subscription**: Credit packages and billing
- **Transaction**: Credit spending/earning transactions

### Key Relationships
- Users belong to Organizations (many-to-many with roles)
- Models have ModelVersions with system version support
- ImageCompletions reference Models used for generation
- All entities support soft deletion and audit trails

## Queue System Architecture

The application uses AWS SQS queues for processing different types of AI workloads:

### Queue Types
- `images_v1/v2/v3`: Different system versions for image generation
- `images_fast`: Priority image generation
- `images_vip`: VIP user queue
- `models_v1/v2/v3`: Model training queues
- `video`: Video processing
- `upscale`: Image upscaling
- `image_resizer`: Post-processing image resizing
- `image_edit`: Image editing tasks

### Queue Processing
- Tasks are enqueued via SQS managers in service classes
- LocalStack provides local AWS service emulation for development
- Production uses real AWS SQS with different queue URLs per environment

## Authentication & Security

### Authentication Methods
- JWT tokens for session management
- OAuth providers: Facebook, Google, Apple
- SAML SSO for enterprise organizations
- API keys for integration access

### Security Features
- Request validation with class-validator
- API rate limiting and request logging
- User and organization-level authorization
- Soft deletion for data retention
- Audit logging via Pino

## Development Guidelines

### Module Structure Pattern
Each business domain follows this structure:
```
module/
├── controller/        # REST endpoints
├── service/          # Business logic
├── entity/           # TypeORM entities
├── dto/              # Request/response DTOs
├── enum/             # Enums and constants
├── event/            # Domain events
└── module.ts         # Module definition
```

### Database Patterns
- All entities use UUIDs as primary keys
- Soft deletion with `deletedAt` timestamp
- Audit fields: `createdAt`, `updatedAt`
- JSON columns for flexible schema (settings, metadata)
- Proper indexing on foreign keys and query columns

### Error Handling
- Global exception filters for TypeORM errors
- Sentry integration for error tracking
- Structured logging with request IDs
- Custom validation pipes with detailed error messages

## Environment Variables

### Required Configuration
- Database: `POSTGRES_*` variables
- AWS: `AWS_*` variables or LocalStack URLs
- Authentication: JWT secrets, OAuth client credentials
- Queues: SQS queue URLs for different services
- External: Stripe, OpenAI, Sentry configurations

### Development vs Production
- Use `.env` for local development
- `.env.docker` for containerized development
- Production uses environment-specific variables

## API Documentation

- Swagger UI available at `/doc` endpoint (requires basic auth in development)
- API supports both private and public endpoints based on configuration
- All endpoints use standard REST conventions
- Request/response validation with detailed error messages

This codebase implements a sophisticated AI platform with proper separation of concerns, scalable queue-based processing, and comprehensive business logic for AI model and image generation services.
