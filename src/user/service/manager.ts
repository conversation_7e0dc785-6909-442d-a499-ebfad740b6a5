import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcryptjs';
import { Logger } from 'nestjs-pino';
import * as path from 'path';
import { ModelEntity } from 'src/model/entity/model.entity';
import { OrganizationUserManager } from 'src/organization/service/organization-user.manager';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AppConfigurationService } from '../../core/service/app-configuration.service';
import { Mailer } from '../../core/service/mailer.service';
import { OrganizationUserEntity } from '../../organization/entity/organization-user.entity';
import { UserEntity } from '../entity/user.entity';
import { UserCreatedEvent } from '../event/user-created.event';
import { UserEmailValidatedEvent } from '../event/user-email-validated.event';
import { UserDeletedEvent } from '../event/user-deleted.event';

@Injectable()
export class UserManager {
  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    private appConfig: AppConfigurationService,
    private mailer: Mailer,
    private eventEmitter: EventEmitter2,
    private logger: Logger,
    private organizationUserManager: OrganizationUserManager,
  ) {}

  async create(user: UserEntity, organizationId?: string): Promise<UserEntity> {
    if (!user.systemVersion) {
      user.systemVersion = ModelEntity.defaultSystemVersion;
    }

    await this.userRepository.save(user);

    if (organizationId) {
      const organizationUser = new OrganizationUserEntity();
      organizationUser.user = user;
      organizationUser.organizationId = organizationId;

      await this.organizationUserManager.create(organizationUser);
    }

    this.eventEmitter.emit(
      'user.created',
      new UserCreatedEvent({
        id: user.id,
      }),
    );

    return user;
  }

  async update(user: UserEntity): Promise<UserEntity> {
    return await this.userRepository.save(user);
  }

  async delete(user: UserEntity): Promise<void> {
    const event = new UserDeletedEvent({
      id: user.id,
      username: user.username,
    });

    await this.userRepository.softDelete(user.id);

    this.eventEmitter.emit('user.deleted', event);
  }

  async generateEmailValidationCode(user: UserEntity) {
    const randomPart =
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
    const datePart = new Date().getTime().toString(36);

    user.emailValidationCode = `${datePart}-${randomPart}`;

    const expiryDate = new Date();
    expiryDate.setHours(expiryDate.getHours() + 48);
    user.emailValidationCodeExpiresAt = expiryDate;

    return await this.userRepository.save(user);
  }

  async validateEmail(user: UserEntity, code: string): Promise<UserEntity> {
    this.logger.log('user.validate_email', {
      userId: user.id,
      code: code,
    });

    // Check if the code exists and matches
    if (!user.emailValidationCode || user.emailValidationCode !== code) {
      throw new Error('InvalidCode');
    }

    // Check if the code has expired
    const now = new Date();
    if (
      !user.emailValidationCodeExpiresAt ||
      user.emailValidationCodeExpiresAt < now
    ) {
      throw new Error('ExpiredCode');
    }

    // Mark email as validated and clear the code and expiration
    user.emailValidatedAt = new Date();
    user.emailValidationCode = null;
    user.emailValidationCodeExpiresAt = null;

    await this.userRepository.save(user);

    this.eventEmitter.emit(
      'user.email_validated',
      new UserEmailValidatedEvent({
        id: user.id,
      }),
    );

    return user;
  }

  async sendValidationEmail(user: UserEntity) {
    const subject = `Welcome to letz.ai! Activate your account now!`;

    const replacements = {
      subject: subject,
      username: user.username || user.name,
      activationLink: `${this.appConfig.asString(
        'FRONTEND_URL',
      )}/user/validate-email/${user.id}?code=${user.emailValidationCode}`,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'user',
      'email-validation.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      this.logger.error('Failed to send email validation email:', {
        userId: user.id,
        error: error,
      });
    }
  }

  validateUserPassword(user: UserEntity, password: string): boolean {
    if (null === user.password) {
      return true;
    }

    return bcrypt.compareSync(password, user.password);
  }

  encryptPassword(password: string): string {
    return bcrypt.hashSync(password, 10);
  }

  async initiatePasswordReset(user: UserEntity) {
    const resetToken = uuidv4();

    user.resetPasswordToken = resetToken;
    const expirationDate = new Date();
    expirationDate.setHours(expirationDate.getHours() + 1);
    user.resetPasswordExpires = expirationDate;

    await this.update(user);

    const subject = `Password Reset Request for Your Account`;
    const resetLink = `${this.appConfig.asString(
      'FRONTEND_URL',
    )}/user/reset-password?userId=${user.id}&token=${resetToken}`;
    const replacements = {
      subject: subject,
      username: user.username || user.name,
      resetLink: resetLink,
    };

    const template = path.join(
      this.appConfig.templateDir,
      'email',
      'user',
      'password-reset.html',
    );

    try {
      await this.mailer.send(template, replacements, subject, user.email);
    } catch (error) {
      console.error('Failed to send password reset email:', error);
    }
  }
}
