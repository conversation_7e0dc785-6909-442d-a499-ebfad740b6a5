import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsIn,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';
import { QueueEnum } from '../../image-completion/entity/image-completion.entity';

export class UserUpdateRequest {
  @IsOptional()
  @IsString()
  @ApiProperty()
  name: string;

  @IsOptional()
  @IsEmail()
  @ApiProperty()
  email: string;

  @IsOptional()
  @ApiProperty()
  @Matches(/^[a-zA-Z0-9_.]+$/, {
    message:
      'Username must contain only alphanumeric characters, underscores, and dots',
  })
  username: string;

  @IsOptional()
  @IsUUID()
  @ApiProperty()
  profilePicture: string;

  @IsOptional()
  @ApiProperty()
  description: string;

  @IsOptional()
  @ApiProperty()
  website: string;

  @ApiProperty()
  socialMediaAccounts: any;

  @IsOptional()
  @ApiProperty()
  timezone: string;

  @IsOptional()
  @ApiProperty()
  currency: string;

  @IsDate()
  @IsOptional()
  @ApiProperty()
  birthday: Date;

  @IsOptional()
  @ApiProperty()
  @IsIn([3])
  systemVersion: number;

  @ApiProperty()
  @IsEnum(QueueEnum, {
    message: 'Queue must be a valid value',
  })
  @ApiProperty({ enum: QueueEnum })
  imageQueue: QueueEnum = QueueEnum.SLOW;

  @IsOptional()
  @ApiProperty()
  locale: string;

  @IsOptional()
  @ApiProperty()
  includeWatermarks: boolean;

  @ApiProperty({ default: false })
  @IsBoolean()
  hidePrompt = false;

  @IsOptional()
  @IsObject()
  @ApiProperty()
  tutorialSteps: any;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  isBot?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty()
  allowSwapping: boolean;
}
