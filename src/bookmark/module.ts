import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BookmarkEntity } from './entity/bookmark.entity';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController } from './controller/read.controller';
import { BookmarkProvider } from './service/bookmark.provider';
import { BookmarkManager } from './service/bookmark.manager';
import { BookmarkRequestManager } from './service/bookmark.request-manager';
import { BookmarkResponseMapper } from './service/bookmark.response-mapper';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ModelModule } from 'src/model/module';
import { ImageDeletedListener } from './listener/image-deleted.listener';
import { ModelDeletedListener } from './listener/model-deleted.listener';
import { VideoModule } from 'src/video/module';

@Module({
  imports: [
    TypeOrmModule.forFeature([BookmarkEntity]),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => ModelModule),
    forwardRef(() => VideoModule),
  ],
  exports: [BookmarkProvider],
  providers: [
    BookmarkProvider,
    BookmarkManager,
    BookmarkRequestManager,
    BookmarkResponseMapper,
    ImageDeletedListener,
    ModelDeletedListener,
  ],
})
export class BookmarkModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: BookmarkModule,
      controllers: enableControllers
        ? [CreateController, DeleteController, ReadController]
        : [],
    };
  }
}
