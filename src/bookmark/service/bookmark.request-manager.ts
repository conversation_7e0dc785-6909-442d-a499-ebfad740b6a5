import { Injectable, NotFoundException } from '@nestjs/common';
import { BookmarkEntity } from '../entity/bookmark.entity';
import { BookmarkRequest } from '../dto/bookmark.request';
import { BookmarkProvider } from './bookmark.provider';
import { BookmarkManager } from './bookmark.manager';
import { ModelProvider } from 'src/model/service/provider';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { VideoProvider } from 'src/video/service/provider';

@Injectable()
export class BookmarkRequestManager {
  constructor(
    private readonly provider: BookmarkProvider,
    private readonly manager: BookmarkManager,
    private readonly modelProvider: ModelProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly videoProvider: VideoProvider,
  ) {}

  async create(request: BookmarkRequest, user: any): Promise<BookmarkEntity> {
    const newBookmark = new BookmarkEntity();
    newBookmark.userId = user.id;
    newBookmark.type = request.type;
    newBookmark.referenceId = request.referenceId;

    // Validate the existence of the referenced entity
    if (request.type === 'model') {
      const model = await this.modelProvider.get(request.referenceId);
      if (!model) {
        throw new NotFoundException(
          `Model with ID ${request.referenceId} not found`,
        );
      }
    } else if (request.type === 'image') {
      const imageCompletion = await this.imageCompletionProvider.get(
        request.referenceId,
      );
      if (!imageCompletion) {
        throw new NotFoundException(
          `Image completion with ID ${request.referenceId} not found`,
        );
      }
    } else if (request.type === 'video') {
      const video = await this.videoProvider.get(request.referenceId);
      if (!video) {
        throw new NotFoundException(
          `Video with ID ${request.referenceId} not found`,
        );
      }
    }

    return this.manager.create(newBookmark);
  }

  async delete(referenceId: string, userId: string): Promise<void> {
    const entity = await this.provider.getBy({
      referenceId: referenceId,
      userId: userId,
    });
    if (!entity || entity.userId !== userId) {
      throw new NotFoundException(
        `Bookmark with ReferenceID ${referenceId} not found`,
      );
    }
    await this.manager.delete(entity);
  }
}
