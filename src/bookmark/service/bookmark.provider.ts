import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsOrder, Repository } from 'typeorm';
import { BookmarkEntity } from '../entity/bookmark.entity';

@Injectable()
export class BookmarkProvider {
  constructor(
    @InjectRepository(BookmarkEntity)
    private readonly repository: Repository<BookmarkEntity>,
  ) {}

  async get(id: string): Promise<BookmarkEntity | undefined> {
    return this.repository.findOne({ where: { id } });
  }

  async getBy(criteria) {
    return this.repository.findOne({ where: criteria });
  }

  async findBy(
    filters: Partial<BookmarkEntity>,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder = 'ASC',
  ): Promise<BookmarkEntity[]> {
    const order: Record<string, 'ASC' | 'DESC'> = {};
    if (sortBy) {
      order[sortBy] = (sortOrder ?? 'ASC') as 'ASC' | 'DESC';
    }

    return this.repository.find({
      where: filters,
      skip: (page - 1) * limit,
      take: limit,
      order: order as FindOptionsOrder<BookmarkEntity>,
    });
  }

  async isBookmarked(
    referenceId: string,
    type: 'model' | 'image' | 'video',
    userId: string,
  ): Promise<boolean> {
    return this.repository.exist({
      where: {
        referenceId: referenceId,
        type: type,
        userId: userId,
      },
    });
  }

  async countBy(filters: Partial<BookmarkEntity>): Promise<number> {
    return this.repository.count({ where: filters });
  }
}
