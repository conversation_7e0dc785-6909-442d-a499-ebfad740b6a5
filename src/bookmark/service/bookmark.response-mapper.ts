import { Injectable } from '@nestjs/common';
import { BookmarkEntity } from '../entity/bookmark.entity';
import { BookmarkDto } from '../dto/bookmark.dto';
import { ModelProvider } from 'src/model/service/provider';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { ModelResponseMapper } from 'src/model/service/response-mapper';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { VideoResponseMapper } from 'src/video/service/response-mapper';
import { VideoProvider } from '../../video/service/provider';

@Injectable()
export class BookmarkResponseMapper {
  constructor(
    private readonly modelProvider: ModelProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
    private readonly modelResponseMapper: ModelResponseMapper,
    private readonly imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private readonly videoResponseMapper: VideoResponseMapper,
    private readonly videoProvider: VideoProvider,
  ) {}

  async map(
    entity: BookmarkEntity,
    userId: string = null,
  ): Promise<BookmarkDto> {
    let bookmarkDetails;
    if (entity.type === 'model') {
      const model = await this.modelProvider.get(entity.referenceId);
      bookmarkDetails = model
        ? await this.modelResponseMapper.map(model)
        : null;
    } else if (entity.type === 'image') {
      const imageCompletion = await this.imageCompletionProvider.get(
        entity.referenceId,
      );
      bookmarkDetails = imageCompletion
        ? await this.imageCompletionResponseMapper.map(
            imageCompletion,
            true,
            true,
            userId,
          )
        : null;
    } else if (entity.type === 'video') {
      const video = await this.videoProvider.get(entity.referenceId);
      bookmarkDetails = video
        ? await this.videoResponseMapper.map(video, userId)
        : null;
    }

    return {
      id: entity.id,
      type: entity.type,
      referenceId: entity.referenceId,
      createdAt: entity.createdAt,
      bookmarkDetails,
    };
  }

  async mapMultiple(
    entities: BookmarkEntity[],
    userId: string = null,
  ): Promise<BookmarkDto[]> {
    return Promise.all(entities.map((entity) => this.map(entity, userId)));
  }
}
