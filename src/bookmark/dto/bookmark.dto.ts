import { ApiProperty } from '@nestjs/swagger';

export class BookmarkDto {
  @ApiProperty({ description: 'Unique identifier for the bookmark' })
  id: string;

  @ApiProperty({ description: 'Type of the bookmark (model or image)' })
  type: 'model' | 'image' | 'video';

  @ApiProperty({ description: 'Reference ID of the bookmarked entity' })
  referenceId: string;

  @ApiProperty({ description: 'Creation date of the bookmark' })
  createdAt: Date;

  @ApiProperty({
    description: 'Details of the bookmarked entity',
    required: false,
  })
  bookmarkDetails?: any; // This can be more specific if you have a common interface or class
}
