import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsIn } from 'class-validator';

export class BookmarkRequest {
  @ApiProperty({ description: 'Type of the bookmark (model or image)' })
  @IsString()
  @IsIn(['model', 'image', 'video'])
  type: 'model' | 'image' | 'video';

  @ApiProperty({ description: 'Reference ID of the bookmarked entity' })
  @IsString()
  @IsNotEmpty()
  referenceId: string;
}
