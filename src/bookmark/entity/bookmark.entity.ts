import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity('bookmarks')
@Index('idx_bookmark_user_type_ref', ['userId', 'type', 'referenceId'], {
  unique: true,
}) // Named composite unique index
export class BookmarkEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index('idx_bookmark_user_id') // Named index on userId
  userId: string;

  @Column()
  type: 'model' | 'image' | 'video';

  @Column()
  @Index('idx_bookmark_reference_id') // Named index on referenceId
  referenceId: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;
}
