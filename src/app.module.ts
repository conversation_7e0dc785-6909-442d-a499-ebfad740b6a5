import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { config } from 'dotenv';
import * as Jo<PERSON> from 'joi';
import { LoggerModule } from 'nestjs-pino';
import { randomUUID } from 'node:crypto';
import { AuthModule } from './auth/auth.module';
import { ApiKeyGuard } from './auth/service/api-key.guard';
import { CombinedGuard } from './auth/service/combined.guard';
import { JwtAuthGuard } from './auth/service/jwt-auth.guard';
import { JwtIntegrationAuthGuard } from './auth/service/jwt-integration-auth.guard';
import { BoardModule } from './board/board.module';
import { BookmarkModule } from './bookmark/module';
import { CoreModule } from './core/core.module';
import { TypeOrmConfigService } from './core/typeorm/typeorm.service';
import { FeedModule } from './feed/module';
import { ImageCompletionModule } from './image-completion/module';
import { ImageEditModule } from './image-edit/image-edit.module';
import { InternalModule } from './internal/module';
import { ModelModule } from './model/module';
import { NotificationModule } from './notification/module';
import { OrganizationModule } from './organization/organization.module';
import { PublicApiModule } from './public-api/module';
import { SearchModule } from './search/module';
import { StatisticsModule } from './statistics/module';
import { SubscriptionModule } from './subscription/module';
import { UpscaleModule } from './upscale/module';
import { UserExternalProfileModule } from './user-external-profile/module';
import { UserImageModule } from './user-image/user-image.module';
import { UserModule } from './user/user.module';
import { ChatModule } from './chat/chat.module';
import { VideoModule } from './video/module';

config();

const loggerConfig = {
  pinoHttp: {
    redact: ['request.headers.authorization'],
    customLogLevel: function (req, res, err) {
      if (req.originalUrl === '/ping') {
        return 'silent';
      }

      if (res.statusCode >= 400 && res.statusCode < 500) {
        return 'warn';
      }

      if (res.statusCode >= 500 || err) {
        return 'error';
      }

      return 'info';
    },
    genReqId: function (req, res) {
      const existingID = req.id ?? req.headers['x-request-id'];
      if (existingID) return existingID;
      const id = randomUUID();
      res.setHeader('X-Request-Id', id);
      return id;
    },
    mixin: (context, level) => {
      return { logId: randomUUID() }; // Add unique logId to each log
    },
    quietReqLogger: true,
    transport:
      process.env.NODE_ENV !== 'production'
        ? {
            target: 'pino-pretty',
            options: {
              colorize: true,
            },
          }
        : undefined,
  },
  customSuccessMessage: function (req, res) {
    if (res.statusCode >= 200 && res.statusCode <= 299) {
      return `${req.method} completed`;
    }

    return `${req.method} failed`;
  },
};

const controllersEnablePublic =
  process.env.CONTROLLERS_ENABLE_PUBLIC === 'true';
const controllersEnablePrivate =
  process.env.CONTROLLERS_ENABLE_PRIVATE === 'true';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.NODE_ENV !== 'production' ? '.env' : undefined,
      ignoreEnvFile: process.env.NODE_ENV === 'production',
      isGlobal: true,
      validationSchema: Joi.object({
        AWS_ACCESS_KEY_ID: Joi.string().optional(),
        AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
        AWS_REGION: Joi.string().optional(),
        CDN_HOST: Joi.string().optional(),
        CONTROLLERS_ENABLE_PUBLIC: Joi.boolean().optional().default(false),
        CONTROLLERS_ENABLE_PRIVATE: Joi.boolean().optional().default(true),
        ENABLE_LOCAL_CACHE: Joi.boolean().optional().default(true),
        LOCAL_CACHE_TTL: Joi.number().optional().default(300000),
        LOCAL_CACHE_MAX_ITEMS: Joi.number().optional().default(100),
        FACEBOOK_CLIENT_ID: Joi.string().required(),
        FACEBOOK_CLIENT_SECRET: Joi.string().required(),
        IMAGE_COMPLETION_S3_BUCKET_NAME: Joi.string().required(),
        IMAGE_COMPLETION_SQS_QUEUE_URL_V1: Joi.string().required(),
        IMAGE_COMPLETION_SQS_QUEUE_URL_V2: Joi.string().required(),
        IMAGE_COMPLETION_SQS_QUEUE_URL_V3: Joi.string().required(),
        IMAGE_COMPLETION_SQS_QUEUE_URL_FAST: Joi.string().required(),
        IMAGE_COMPLETION_SQS_QUEUE_URL_VIP: Joi.string().required(),
        IMAGE_EDIT_SQS_QUEUE_URL: Joi.string().required(),
        IMAGE_RESIZER_SQS_QUEUE_URL: Joi.string().required(),
        INTERNAL_API_KEY: Joi.string().required(),
        JWT_SECRET: Joi.string().required(),
        LOCALSTACK_S3_URL: Joi.string().optional(),
        LOCALSTACK_SQS_URL: Joi.string().optional(),
        MODEL_S3_BUCKET_NAME_V1: Joi.string().required(),
        MODEL_S3_BUCKET_NAME_V2: Joi.string().required(),
        MODEL_S3_BUCKET_NAME_V3: Joi.string().required(),
        MODEL_SQS_QUEUE_URL_V1: Joi.string().required(),
        MODEL_SQS_QUEUE_URL_V2: Joi.string().required(),
        MODEL_SQS_QUEUE_URL_V3: Joi.string().required(),
        MODEL_SQS_QUEUE_URL_V3_RETRAINING: Joi.string().optional(),
        MODEL_SQS_QUEUE_URL_VIP: Joi.string().required(),
        OPENAI_API_KEY: Joi.string().required(),
        ORGANIZATION_DEFAULT_HANDLE: Joi.string().optional(),
        POSTGRES_HOST: Joi.string().required(),
        POSTGRES_PORT: Joi.string().required(),
        POSTGRES_DB: Joi.string().required(),
        POSTGRES_USER: Joi.string().required(),
        POSTGRES_PASSWORD: Joi.string().required(),
        STRIPE_API_KEY: Joi.string().required(),
        STRIPE_WH_SECRET: Joi.string().required(),
        UPSCALE_SQS_QUEUE_URL: Joi.string().required(),
        USER_IMAGE_S3_BUCKET_NAME: Joi.string().optional(),
      }),
    }),
    SentryModule.forRoot(),
    LoggerModule.forRoot(loggerConfig),
    EventEmitterModule.forRoot({
      ignoreErrors: false,
    }),
    TypeOrmModule.forRootAsync({ useClass: TypeOrmConfigService }),
    AuthModule.register(controllersEnablePrivate),
    BookmarkModule.register(controllersEnablePrivate),
    BoardModule.register(controllersEnablePrivate),
    CoreModule,
    FeedModule.register(controllersEnablePrivate),
    StatisticsModule.register(controllersEnablePrivate),
    InternalModule.register(controllersEnablePrivate),
    ImageCompletionModule.register(controllersEnablePrivate),
    ImageEditModule.register(controllersEnablePrivate),
    ModelModule.register(controllersEnablePrivate),
    OrganizationModule.register(controllersEnablePrivate),
    PublicApiModule.register(controllersEnablePublic),
    SubscriptionModule.register(controllersEnablePrivate),
    UpscaleModule.register(controllersEnablePrivate),
    UserModule.register(controllersEnablePrivate),
    NotificationModule.register(controllersEnablePrivate),
    UserExternalProfileModule.register(controllersEnablePrivate),
    UserImageModule.register(controllersEnablePrivate),
    SearchModule.register(controllersEnablePrivate),
    ChatModule.register(controllersEnablePrivate),
    VideoModule.register(controllersEnablePrivate),
  ],
  providers: [
    JwtAuthGuard,
    JwtIntegrationAuthGuard,
    ApiKeyGuard,
    {
      provide: APP_GUARD,
      useClass: controllersEnablePublic
        ? JwtIntegrationAuthGuard
        : CombinedGuard,
    },
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
  ],
})
export class AppModule {}
