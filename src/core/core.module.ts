import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PingController } from './controller/ping.controller';
import { AppConfigurationService } from './service/app-configuration.service';
import { LocalCacheService } from './service/local-cache.service';
import { Mailer } from './service/mailer.service';
import { OpenAIFactory } from './service/openai.factory';

@Module({
  controllers: [PingController],
  providers: [
    AppConfigurationService,
    LocalCacheService,
    Mailer,
    OpenAIFactory,
  ],
  exports: [AppConfigurationService, LocalCacheService, Mailer, 'OPENAI'],
})
export class CoreModule {}
