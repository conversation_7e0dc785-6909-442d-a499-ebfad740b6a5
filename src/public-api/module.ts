import { DynamicModule, Module } from '@nestjs/common';
import { PublicImageModule } from './image/module';
import { PublicImageEditModule } from './image-edit/module';
import { PublicModelModule } from './model/module';
import { PublicUpscaleModule } from './upscale/module';
import { PublicUserImageModule } from './user-image/module';
import { PublicVideoModule } from './video/module';

@Module({})
export class PublicApiModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicApiModule,
      imports: [
        PublicImageModule.register(enableControllers),
        PublicImageEditModule.register(enableControllers),
        PublicModelModule.register(enableControllers),
        PublicUpscaleModule.register(enableControllers),
        PublicUserImageModule.register(enableControllers),
        PublicVideoModule.register(enableControllers),
      ],
    };
  }
}
