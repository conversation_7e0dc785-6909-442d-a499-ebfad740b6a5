import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';
import { VideoRequest } from 'src/video/dto/video.request';
import { PublicVideoRequest } from '../dto/video.request';

@Injectable()
export class VideoPublicRequestManager {
  constructor(private userProvider: UserProvider) {}

  async sanitizeSearchFilters(filters: any): Promise<any> {
    const where = { ...filters };

    // If username provided, resolve to userId
    if (where.hasOwnProperty('username')) {
      const user = await this.userProvider.getBy({ username: where.username });
      where.userId = user?.id;
      delete where.username;
    }

    // Videos are surfaced for public consumption only when their original image
    // is public, ready, and non-NSFW. The provider enforces this.

    return where;
  }

  async sanitizeCreateRequest(
    request: PublicVideoRequest,
    user: any,
  ): Promise<VideoRequest> {
    // Validate that either originalImageCompletionId or imageUrl is provided
    if (!request.originalImageCompletionId && !request.imageUrl) {
      throw new Error(
        'Either originalImageCompletionId or imageUrl must be provided',
      );
    }

    const sanitized = {
      ...request,
      userId: user.id,
    };

    // Set organization context if user has one and none provided
    if (user.organizationId && !sanitized.organizationId) {
      sanitized.organizationId = user.organizationId;
    }

    // Set defaults
    if (!sanitized.imageCompletionsCount) {
      sanitized.imageCompletionsCount = 1;
    }

    return sanitized;
  }
}
