import { Injectable } from '@nestjs/common';
import { UserProvider } from 'src/user/service/provider';

@Injectable()
export class VideoPublicRequestManager {
  constructor(private userProvider: UserProvider) {}

  async sanitizeSearchFilters(filters: any): Promise<any> {
    const where = { ...filters };

    // If username provided, resolve to userId
    if (where.hasOwnProperty('username')) {
      const user = await this.userProvider.getBy({ username: where.username });
      where.userId = user?.id;
      delete where.username;
    }

    // Videos are surfaced for public consumption only when their original image
    // is public, ready, and non-NSFW. The provider enforces this.

    return where;
  }
}

