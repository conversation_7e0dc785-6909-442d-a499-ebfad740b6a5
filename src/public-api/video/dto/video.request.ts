import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  IsUUI<PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

export class PublicVideoRequest {
  @ApiProperty({
    description: 'Original image completion ID to create video from',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  originalImageCompletionId?: string;

  @ApiProperty({
    description: 'Image URL to create video from (alternative to originalImageCompletionId)',
    required: false,
    example: 'https://example.com/image.jpg',
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty({
    description: 'Number of image completions to generate for the video',
    required: false,
    minimum: 1,
    maximum: 5,
    default: 1,
    example: 1,
  })
  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(5)
  imageCompletionsCount? = 1;

  @ApiProperty({
    description: 'Organization ID (for organization accounts)',
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty({
    description: 'Width of the video',
    required: false,
    example: 1024,
  })
  @IsInt()
  @IsOptional()
  width?: number;

  @ApiProperty({
    description: 'Height of the video',
    required: false,
    example: 1024,
  })
  @IsInt()
  @IsOptional()
  height?: number;

  @ApiProperty({
    description: 'Resolution of the video',
    required: false,
    example: 1024,
  })
  @IsInt()
  @IsOptional()
  resolution?: number;

  @ApiProperty({
    description: 'Prompt for video generation',
    required: true,
    example: 'A beautiful sunset over the ocean',
  })
  @IsString()
  prompt: string;

  @ApiProperty({
    description: 'Webhook URL to receive notifications about video generation progress',
    required: false,
    example: 'https://your-app.com/webhook/video-complete',
  })
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty({
    description: 'Whether to hide the prompt in public listings',
    required: false,
    default: false,
    example: false,
  })
  @IsBoolean()
  hidePrompt = false;

  @ApiProperty({
    description: 'Additional settings for video generation',
    required: false,
    example: { quality: 'high', fps: 30 },
  })
  @IsOptional()
  @IsObject()
  settings?: any;
}
