import {
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { VideoDto } from 'src/video/dto/video.dto';
import { VideoProvider } from 'src/video/service/provider';
import { VideoResponseMapper } from 'src/video/service/response-mapper';
import { VideoSearchRequest } from 'src/video/dto/video.search-request';
import { VideoPublicRequestManager } from '../service/request-manager';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private provider: VideoProvider,
    private responseMapper: VideoResponseMapper,
    private requestManager: VideoPublicRequestManager,
  ) {}

  @Get()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'public_video_list',
    summary: 'List videos (public API)',
    description:
      'Retrieves a paginated list of videos.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number (default: 1)\n' +
      '- limit: Items per page (1-50, default: 10)\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: "ASC" or "DESC"\n' +
      '- userId: Filter by user ID\n' +
      '- username: Filter by username\n' +
      '- status: Filter by video status\n' +
      '- width: Filter by video width\n' +
      '- height: Filter by video height\n' +
      '- resolution: Filter by video resolution\n' +
      '- prompt: Filter by prompt text\n' +
      '- hidePrompt: Filter by hidden prompts\n\n' +
      'Note: Only videos based on public, ready, non-NSFW images are returned.',
  })
  @ApiOkResponse({
    type: VideoDto,
    isArray: true,
    description: 'Paginated list of videos',
  })
  @ApiQuery({
    type: VideoSearchRequest,
    description: 'Query parameters for filtering videos',
  })
  @ApiResponse({
    status: 400,
    description: `Bad Request. Possible reasons:\n
      - Pagination parameters (page, limit) must be positive integers between 1 and 50.
      - Sort parameters (sortBy, sortOrder) must be valid values.
      - Filter parameters must be valid values.
      - userId: Must be a valid UUID.
      - username: Must be a valid username.
      - status: Must be a valid video status.
      - width/height/resolution: Must be valid numbers.
    `,
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving videos.',
  })
  async find(
    @Query() query: VideoSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.requestManager.sanitizeSearchFilters(
      inputFilters,
    );

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'public_video_get',
    summary: 'Get video by ID (public API)',
    description:
      'Retrieves a specific video by its UUID.\n\n' +
      'Required Parameters:\n' +
      '- id: UUID of the video\n\n' +
      'Note: Only videos based on public, ready, non-NSFW images are accessible.',
  })
  @ApiOkResponse({
    type: VideoDto,
    description: 'Returns the video with the specified ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the video to retrieve.',
    type: String,
  })
  @ApiResponse({
    status: 400,
    description: `Bad Request. Possible reasons:\n
      - id: Must be a valid UUID.
      - Video does not exist.
      - Invalid or unavailable parameters.
    `,
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. Video with the specified ID does not exist.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred while retrieving the video.',
  })
  async get(@Param('id', new ParseUUIDPipe()) id: string): Promise<VideoDto> {
    const video = await this.provider.get(id);
    return await this.responseMapper.map(video);
  }
}
