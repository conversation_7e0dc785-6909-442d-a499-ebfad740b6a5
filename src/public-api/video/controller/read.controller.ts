import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { VideoDto } from 'src/video/dto/video.dto';
import { VideoProvider } from 'src/video/service/provider';
import { VideoResponseMapper } from 'src/video/service/response-mapper';
import { VideoSearchRequest } from 'src/video/dto/video.search-request';
import { VideoPublicRequestManager } from '../service/request-manager';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private provider: VideoProvider,
    private responseMapper: VideoResponseMapper,
    private requestManager: VideoPublicRequestManager,
  ) {}

  @Get()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'public_video_list',
    summary: 'List videos (public API)',
    description:
      'Retrieves a paginated list of videos. Filterable by username or userId.',
  })
  @ApiOkResponse({
    type: VideoDto,
    isArray: true,
    description: 'Paginated list of videos',
  })
  @ApiQuery({
    type: VideoSearchRequest,
    description: 'Query parameters for filtering videos',
  })
  async find(
    @Query() query: VideoSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = await this.requestManager.sanitizeSearchFilters(
      inputFilters,
    );

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @ApiOperation({
    operationId: 'public_video_get',
    summary: 'Get video by ID (public API)',
  })
  @ApiOkResponse({ type: VideoDto })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  async get(@Param('id', new ParseUUIDPipe()) id: string): Promise<VideoDto> {
    const video = await this.provider.get(id);
    return await this.responseMapper.map(video);
  }
}
