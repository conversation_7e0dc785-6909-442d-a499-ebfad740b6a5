import {
  Body,
  Controller,
  Post,
  Request,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { VideoDto } from 'src/video/dto/video.dto';
import { VideoRequestManager } from 'src/video/service/request-manager';
import { VideoResponseMapper } from 'src/video/service/response-mapper';
import { PublicVideoRequest } from '../dto/video.request';
import { VideoPublicRequestManager } from '../service/request-manager';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class CreateController {
  constructor(
    private videoRequestManager: VideoRequestManager,
    private responseMapper: VideoResponseMapper,
    private requestManager: VideoPublicRequestManager,
  ) {}

  @Post()
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'public_video_create',
    summary: 'Create a new video (public API)',
    description:
      'Creates a new video generation request.\n\n' +
      'Required Parameters:\n' +
      '- prompt: Text description for the video\n' +
      '- Either originalImageCompletionId OR imageUrl must be provided\n\n' +
      'Optional Parameters:\n' +
      '- imageCompletionsCount: Number of image completions to generate (1-5, default: 1)\n' +
      '- organizationId: Organization ID for organization accounts\n' +
      '- width: Video width in pixels\n' +
      '- height: Video height in pixels\n' +
      '- resolution: Video resolution\n' +
      '- webhookUrl: URL to receive progress notifications\n' +
      '- hidePrompt: Whether to hide prompt in public listings (default: false)\n' +
      '- settings: Additional generation settings\n\n' +
      'Authentication: Requires valid JWT token.\n\n' +
      'Rate Limits: Subject to user account limits.',
  })
  @ApiBody({
    type: PublicVideoRequest,
    description: 'Video creation request parameters',
  })
  @ApiCreatedResponse({
    type: VideoDto,
    description: 'Video creation request submitted successfully',
  })
  @ApiResponse({
    status: 400,
    description: `Bad Request. Possible reasons:\n
      - Missing required fields (prompt, originalImageCompletionId or imageUrl)
      - Invalid field values or formats
      - imageCompletionsCount must be between 1 and 5
      - Invalid UUID format for originalImageCompletionId or organizationId
      - Invalid URL format for imageUrl or webhookUrl
      - Width, height, resolution must be positive integers
      - Settings must be a valid object
    `,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Valid JWT token required.',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden. User does not have permission to create videos.',
  })
  @ApiResponse({
    status: 404,
    description:
      'Not Found. Referenced originalImageCompletionId does not exist.',
  })
  @ApiResponse({
    status: 429,
    description: 'Too Many Requests. Rate limit exceeded.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal Server Error. An unexpected error occurred during video creation.',
  })
  async create(
    @Body() body: PublicVideoRequest,
    @Request() request: any,
  ): Promise<VideoDto> {
    const sanitizedRequest = await this.requestManager.sanitizeCreateRequest(
      body,
      request.user,
    );

    const video = await this.videoRequestManager.create(
      sanitizedRequest,
      request.user,
    );
    return await this.responseMapper.map(video);
  }
}
