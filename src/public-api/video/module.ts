import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthModule } from 'src/auth/auth.module';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { VideoModule } from 'src/video/module';
import { UserModule } from 'src/user/user.module';
import { CreateController } from './controller/create.controller';
import { ReadController } from './controller/read.controller';
import { VideoPublicRequestManager } from './service/request-manager';

@Module({})
export class PublicVideoModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: PublicVideoModule,
      providers: [VideoPublicRequestManager],
      imports: [
        PassportModule,
        JwtModule.register(jwtConfig),
        forwardRef(() => AuthModule),
        forwardRef(() => UserModule),
        forwardRef(() => VideoModule),
      ],
      controllers: enableControllers ? [ReadController, CreateController] : [],
    };
  }
}
