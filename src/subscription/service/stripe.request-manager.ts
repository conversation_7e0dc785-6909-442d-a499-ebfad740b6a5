import {
  BadRequestException,
  Inject,
  Injectable,
  RawBodyRequest,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { Logger } from 'nestjs-pino';
import Stripe from 'stripe';
import { StripeWebhookHandler } from '../../organization/service/stripe.webhook-handler';
import { SubscriptionManager } from './manager';
import { SubscriptionProvider } from './provider';

@Injectable()
export class StripeRequestManager {
  constructor(
    @Inject('Stripe') private readonly stripe: Stripe,
    private readonly subscriptionProvider: SubscriptionProvider,
    private readonly subscriptionManager: SubscriptionManager,
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly organizationWebhookHandler: StripeWebhookHandler,
  ) {}

  async handleWebhook(request: RawBodyRequest<Request>): Promise<void> {   
    const sig = request.headers['stripe-signature'];
    let event: Stripe.Event;

    try {
      const endpointSecret = this.configService.get<string>('STRIPE_WH_SECRET');
      
      // Ensure we're using the raw body as a Buffer for signature verification
      const payload = request.rawBody;
      
      if (!payload) {
        throw new Error('Missing request body');
      }
      
      if (!sig) {
        throw new Error('Missing Stripe signature header');
      }

      this.logger.log('stripe.webhook.handle', {
        sig: sig,
        secret: endpointSecret?.substring(0, 3) + '...',
        payload
      });

      event = this.stripe.webhooks.constructEvent(payload, sig, endpointSecret);
    } catch (err) {
      this.logger.error('stripe.webhook.handle', {
        error: err.message,
        headers: request.headers,
        sig,
      });

      throw new BadRequestException(`Failed to construct Stripe event: ${err.message}`);
    }

    try {
      // Check if this is an organization subscription event
      const session = event.data.object as any;
      if (session.metadata?.organizationId) {
        await this.handleOrganizationWebhook(event);
        return;
      }

      // Handle user subscription events
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionPaid(event.data.object);
          break;
        case 'invoice.payment_succeeded':
        case 'invoice.paid':
          await this.handleInvoicePaymentStatusChange(event.data.object);
          break;
        default:
          return;
      }
    } catch (err) {
      throw new BadRequestException('Impossible to handle request');
    }
  }

  private async handleOrganizationWebhook(event: Stripe.Event): Promise<void> {
    switch (event.type) {
      case 'checkout.session.completed':
        await this.organizationWebhookHandler.handleCheckoutSessionCompleted(
          event,
        );
        break;
      case 'customer.subscription.deleted':
        await this.organizationWebhookHandler.handleSubscriptionDeleted(event);
        break;
      case 'customer.subscription.updated':
        await this.organizationWebhookHandler.handleSubscriptionUpdated(event);
        break;
      default:
        this.logger.log('stripe.webhook.unhandled_organization_event', {
          type: event.type,
        });
        return;
    }
  }

  async handleCheckoutSessionPaid(object: any) {
    this.logger.log('stripe.checkout_session_paid', {
      object: object,
    });

    const subscription = await this.subscriptionProvider.getBy({
      stripeCheckoutSessionId: object.id,
    });

    this.logger.log('stripe.checkout_session_paid', {
      subscription: subscription,
    });

    if (subscription.stripeCheckoutSessionId) {
      await this.subscriptionManager.updateStripeCheckoutStatus(subscription);
    } else {
      await this.subscriptionManager.updateStripeSubscriptionStatus(
        subscription,
      );
    }
  }

  async handleInvoicePaymentStatusChange(object: any) {
    this.logger.log('stripe.invoice_payment_status_change', {
      object: object,
    });

    if (!object.subscription) {
      return;
    }

    try {
      const subscription =
        await this.subscriptionProvider.getByExternalReference(
          object.subscription,
        );

      this.logger.log('stripe.invoice_payment_status_change', {
        subscription: subscription,
      });

      this.subscriptionManager.updateStripeSubscriptionStatus(subscription);
    } catch (e) {
      this.logger.error('stripe.invoice_payment_status_change', {
        object: object,
        error: e,
      });
      return;
    }
  }
}
