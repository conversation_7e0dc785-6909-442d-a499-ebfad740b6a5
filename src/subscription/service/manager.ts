import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { Logger } from 'nestjs-pino';
import { Notifier } from 'src/notification/service/notifier';
import { UserEntity } from 'src/user/entity/user.entity';
import { UserManager } from 'src/user/service/manager';
import { UserProvider } from 'src/user/service/provider';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { CreditTypeEnum } from '../entity/credit-type.enum';
import { SubscriptionStatusEnum } from '../entity/subscription-status.enum';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { TransactionTypeEnum } from '../entity/transaction-type.enum';
import { SubscriptionActivatedEvent } from '../event/subscription-activated.event';
import { SubscriptionCanceledEvent } from '../event/subscription-canceled.event';
import { SubscriptionRenewedNotification } from '../notification/subscription-renewed.notification';
import { CreditPackageProvider } from './credit-package.provider';
import { EmailManager } from './email.manager';
import { SubscriptionProvider } from './provider';
import { StripeService } from './stripe.service';
import { TransactionManager } from './transaction.manager';
import { UserCreditBalanceManager } from './user-credit-balance.manager';

@Injectable()
export class SubscriptionManager {
  constructor(
    @InjectRepository(SubscriptionEntity)
    private readonly repository: Repository<SubscriptionEntity>,
    private readonly provider: SubscriptionProvider,
    @Inject('Stripe') private readonly stripe: Stripe,
    private readonly userProvider: UserProvider,
    private readonly userManager: UserManager,
    private readonly creditPackageProvider: CreditPackageProvider,
    private readonly stripeService: StripeService,
    private readonly transactionManager: TransactionManager,
    private readonly configService: ConfigService,
    private readonly userCreditBalanceManager: UserCreditBalanceManager,
    private readonly emailManager: EmailManager,
    private readonly eventEmitter: EventEmitter2,
    private readonly logger: Logger,
    private readonly notifier: Notifier,
  ) {}

  async create(
    newSubscription: SubscriptionEntity,
  ): Promise<SubscriptionEntity> {
    const pendingSubscriptions =
      await this.provider.findPendingSubscriptionsForUser(
        newSubscription.user.id,
      );

    if (pendingSubscriptions.length > 0) {
      throw new BadRequestException('subscription.pending');
    }

    const currentSubscription =
      await this.provider.findCurrentlyActiveSubscriptionForUser(
        newSubscription.user.id,
      );

    newSubscription.status = SubscriptionStatusEnum.NEW;
    await this.repository.save(newSubscription);

    this.logger.log('subscription.create', {
      currentSubscription: currentSubscription,
      newSubscription: newSubscription,
    });

    if (
      currentSubscription?.creditPackage?.isRecurring &&
      newSubscription.creditPackage.isRecurring &&
      currentSubscription?.status == SubscriptionStatusEnum.ACTIVE
    ) {
      await this.updateStripeSubscription(currentSubscription, newSubscription);
    } else if (newSubscription.price > 0) {
      this.logger.log('subscription.create_stripe_checkout', {
        newSubscription: newSubscription,
      });
      await this.createStripeCheckout(newSubscription);
    }

    return newSubscription;
  }

  async updateStripeSubscription(
    currentSubscription: SubscriptionEntity,
    newSubscription: SubscriptionEntity,
  ) {
    this.logger.log('Updating stripe subscription', {
      currentSubscription: currentSubscription,
      newSubscription: newSubscription,
    });

    const stripeSubscription = await this.stripe.subscriptions.retrieve(
      currentSubscription.externalReference,
    );

    // Ensure the new credit package has a Stripe price ID
    const stripePriceId = await this.stripeService.getOrCreateStripePriceId(
      newSubscription.creditPackage,
    );

    if (stripeSubscription.items.data[0].price.id == stripePriceId) {
      this.logger.log('Subscription already has the correct price', {
        newSubscription: newSubscription,
        stripeSubscription: stripeSubscription,
      });

      return;
    }

    if (currentSubscription.creditPackage.price > newSubscription.price) {
      await this.handleDowngradeSubscription(
        newSubscription,
        currentSubscription,
        stripeSubscription,
        stripePriceId,
      );
    } else {
      // Check if this is a monthly-to-yearly upgrade
      const isYearlyUpgrade =
        currentSubscription.creditPackage.expiresAfterMonths === 1 &&
        newSubscription.creditPackage.expiresAfterMonths === 12;

      if (isYearlyUpgrade) {
        await this.handleYearlyUpgrade(
          newSubscription,
          currentSubscription,
          stripeSubscription,
          stripePriceId,
        );
      } else {
        await this.handleUpgradeSubscription(
          newSubscription,
          currentSubscription,
          stripeSubscription,
          stripePriceId,
        );
      }
    }
  }

  async handleDowngradeSubscription(
    newSubscription: SubscriptionEntity,
    currentSubscription: SubscriptionEntity,
    stripeSubscription: Stripe.Subscription,
    stripePriceId: string,
  ) {
    try {
      this.logger.log('Downgrading subscription', {
        currentSubscription: currentSubscription,
        newSubscription: newSubscription,
      });

      await this.sendStripeSubscriptionUpdate(stripeSubscription.id, {
        collection_method: 'charge_automatically',
        proration_behavior: 'none',
        items: [
          {
            id: stripeSubscription.items.data[0].id,
            price: stripePriceId,
          },
        ],
      });

      newSubscription.previousSubscriptionId = currentSubscription.id;
      newSubscription.status = SubscriptionStatusEnum.PENDING;
      newSubscription.externalReference = stripeSubscription.id;

      await this.repository.save(newSubscription);

      await this.updateStripeSubscriptionStatus(newSubscription);
    } catch (error) {
      this.logger.log('subscription.update_error', {
        error: {
          message: error?.message,
          stack: error?.stack,
          code: error?.code,
          type: error?.type,
          name: error?.name,
        },
        currentSubscription: currentSubscription,
        newSubscription: newSubscription,
      });

      throw error;
    }
  }

  async handleUpgradeSubscription(
    newSubscription: SubscriptionEntity,
    currentSubscription: SubscriptionEntity,
    stripeSubscription: Stripe.Subscription,
    stripePriceId: string,
  ) {
    try {
      this.logger.log('subscription.handle_upgrade', {
        currentSubscription: currentSubscription,
        newSubscription: newSubscription,
      });

      const stripeSubscriptionResponse =
        await this.sendStripeSubscriptionUpdate(stripeSubscription.id, {
          billing_cycle_anchor: 'now',
          collection_method: 'charge_automatically',
          proration_behavior: 'none',
          // payment_behavior: 'pending_if_incomplete',
          items: [
            {
              id: stripeSubscription.items.data[0].id,
              price: stripePriceId,
            },
          ],
        });

      newSubscription.previousSubscriptionId = currentSubscription.id;
      newSubscription.externalReference = stripeSubscription.id;
      newSubscription.status = SubscriptionStatusEnum.PENDING;

      await this.updateInvoicePaymentUrl(
        newSubscription,
        stripeSubscriptionResponse.latest_invoice as string,
      );

      await this.repository.save(newSubscription);

      if (stripeSubscriptionResponse.status == 'active') {
        await this.updateStripeSubscriptionStatus(newSubscription);
      }
    } catch (error) {
      this.logger.log('Error updating stripe subscription', {
        error: {
          message: error?.message,
          stack: error?.stack,
          code: error?.code,
          type: error?.type,
          name: error?.name,
        },
        currentSubscription: currentSubscription,
        newSubscription: newSubscription,
      });

      if (error.code == 'resource_missing') {
        this.logger.log('Upgrading with collection_method=send_invoice', {
          subscription: newSubscription,
        });

        const stripeSubscriptionResponse =
          await this.sendStripeSubscriptionUpdate(stripeSubscription.id, {
            billing_cycle_anchor: 'now',
            collection_method: 'send_invoice',
            proration_behavior: 'none',
            payment_behavior: 'default_incomplete',
            days_until_due: 1,
            items: [
              {
                id: stripeSubscription.items.data[0].id,
                price: stripePriceId,
              },
            ],
          });

        newSubscription.previousSubscriptionId = currentSubscription.id;
        newSubscription.status = SubscriptionStatusEnum.PENDING;
        newSubscription.externalReference = stripeSubscription.id;

        await this.updateInvoicePaymentUrl(
          newSubscription,
          stripeSubscriptionResponse.latest_invoice as string,
        );

        await this.sendStripeInvoice(newSubscription);

        await this.repository.save(newSubscription);
      } else {
        throw error;
      }
    }
  }

  async handleYearlyUpgrade(
    newSubscription: SubscriptionEntity,
    currentSubscription: SubscriptionEntity,
    stripeSubscription: Stripe.Subscription,
    stripePriceId: string,
  ) {
    try {
      this.logger.log('subscription.handle_yearly_upgrade', {
        currentSubscription: currentSubscription,
        newSubscription: newSubscription,
      });

      // Calculate proration for the upgrade
      const proratedInvoice = await this.stripeService.calculateProration(
        stripeSubscription,
        stripePriceId,
      );

      this.logger.log('subscription.yearly_upgrade_proration', {
        proratedAmount: proratedInvoice.amount_due,
        subscriptionId: newSubscription.id,
      });

      // Update the Stripe subscription to yearly billing
      const updatedStripeSubscription =
        await this.stripeService.upgradeToYearlySubscription(
          stripeSubscription.id,
          stripePriceId,
        );

      newSubscription.previousSubscriptionId = currentSubscription.id;
      newSubscription.externalReference = updatedStripeSubscription.id;
      newSubscription.status = SubscriptionStatusEnum.PENDING;

      // Update invoice payment URL if there's a pending invoice
      if (updatedStripeSubscription.latest_invoice) {
        await this.updateInvoicePaymentUrl(
          newSubscription,
          updatedStripeSubscription.latest_invoice as string,
        );
      }

      await this.repository.save(newSubscription);

      // If the subscription is immediately active, update its status
      if (updatedStripeSubscription.status === 'active') {
        await this.updateStripeSubscriptionStatus(newSubscription);
      }
    } catch (error) {
      this.logger.error('Error handling yearly upgrade', {
        error: {
          message: error?.message,
          stack: error?.stack,
          code: error?.code,
          type: error?.type,
          name: error?.name,
        },
        currentSubscription: currentSubscription,
        newSubscription: newSubscription,
      });

      throw error;
    }
  }

  async updateInvoicePaymentUrl(
    subscription: SubscriptionEntity,
    stripeInvoiceId: string,
  ): Promise<void> {
    const stripeInvoice = await this.stripe.invoices.retrieve(stripeInvoiceId);

    subscription.stripeCheckoutUrl = stripeInvoice.hosted_invoice_url;
  }

  async sendStripeInvoice(
    subscription: SubscriptionEntity,
    stripeInvoiceId?: string,
  ): Promise<void> {
    if (!stripeInvoiceId) {
      const stripeSubscription = await this.stripe.subscriptions.retrieve(
        subscription.externalReference,
      );

      stripeInvoiceId = stripeSubscription.latest_invoice as string;
    }

    // await this.updateInvoicePaymentUrl(subscription, stripeInvoiceId);

    await this.stripe.invoices.sendInvoice(stripeInvoiceId);
  }

  async sendStripeSubscriptionUpdate(
    stripeSubscriptionId: string,
    parameters: Stripe.SubscriptionUpdateParams,
  ): Promise<Stripe.Response<Stripe.Subscription>> {
    const stripeSubscriptionResponse = await this.stripe.subscriptions.update(
      stripeSubscriptionId,
      parameters,
    );

    this.logger.log('subscription.stripe.subscription_update', {
      stripeSubscription: stripeSubscriptionResponse,
    });

    return stripeSubscriptionResponse;
  }

  async createStripeCheckout(subscription: SubscriptionEntity) {
    const user = await this.userProvider.get(subscription.userId);
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');

    // Ensure the credit package has a Stripe price ID
    const stripePriceId = await this.stripeService.getOrCreateStripePriceId(
      subscription.creditPackage,
    );

    const checkoutParams: Stripe.Checkout.SessionCreateParams = {
      line_items: [
        {
          price: stripePriceId,
          quantity: 1,
        },
      ],
      mode: subscription.creditPackage.isRecurring ? 'subscription' : 'payment',
      customer: await this.getOrCreateStripeCustomerId(user),
      success_url: frontendUrl + '/paymentsuccess?id=' + subscription.id,
      cancel_url: frontendUrl + '/paymentfailure?id=' + subscription.id,
      billing_address_collection: 'required',
      allow_promotion_codes: true,
      automatic_tax: {
        enabled: true,
      },
      customer_update: {
        address: 'auto',
        name: 'auto',
      },
      tax_id_collection: {
        enabled: true,
      },
    };

    if (!subscription.creditPackage.isRecurring) {
      checkoutParams.invoice_creation = {
        enabled: true,
      };
    }

    this.logger.log('stripe.create_checkout_session', checkoutParams);

    const checkoutSession: Stripe.Checkout.Session =
      await this.stripe.checkout.sessions.create(checkoutParams);

    subscription.status = SubscriptionStatusEnum.PENDING;
    subscription.stripeCheckoutUrl = checkoutSession.url;
    subscription.stripeCheckoutSessionId = checkoutSession.id;

    await this.repository.save(subscription);
  }

  async updateStripeCheckoutStatus(
    subscription: SubscriptionEntity,
  ): Promise<void> {
    if (!subscription.stripeCheckoutSessionId) {
      return;
    }

    let checkoutSession: Stripe.Checkout.Session | undefined;

    try {
      checkoutSession = await this.stripe.checkout.sessions.retrieve(
        subscription.stripeCheckoutSessionId,
      );

      this.logger.log('subscription.stripe.update_checkout_status', {
        checkoutSession,
        subscription,
      });

      if (
        checkoutSession.payment_status == 'paid' ||
        checkoutSession.payment_status == 'no_payment_required'
      ) {
        const paidAt = new Date(checkoutSession.created * 1000);

        if (
          subscription.paidAt != null &&
          paidAt.getTime() === subscription.paidAt.getTime()
        ) {
          return;
        }

        if (subscription.creditPackage.isRecurring) {
          subscription.externalReference =
            checkoutSession.subscription as string;

          await this.updateStripeSubscriptionStatus(subscription);
        }

        subscription.paidAt = paidAt;
        subscription.stripeCheckoutUrl = null;

        await this.repository.save(subscription);

        if (!subscription.creditPackage.isRecurring) {
          await this.activateSubscription(subscription);
        }

        return;
      }
    } catch (error) {
      this.logger.error('Error updating stripe checkout status', {
        subscriptionId: subscription.id,
        error: {
          message: error?.message,
          stack: error?.stack,
          code: error?.code,
          type: error?.type,
          name: error?.name,
        },
      });
    }

    if (!checkoutSession || checkoutSession.status == 'expired') {
      await this.repository.softDelete(subscription.id);

      return;
    }
  }

  async updateStripeSubscriptionStatus(subscription: SubscriptionEntity) {
    const stripeSubscription = await this.stripe.subscriptions.retrieve(
      subscription.externalReference,
    );

    this.logger.log('subscription.stripe.update_subscription_status', {
      stripeSubscription: stripeSubscription,
      subscription: subscription,
    });

    if (stripeSubscription.status == 'active') {
      // Set the expiration date from Stripe
      const newExpiration = DateTime.fromSeconds(
        stripeSubscription.current_period_end,
      )
        .setZone('utc')
        .toJSDate();

      // Set the paid/renewed date from Stripe
      const paidDate = DateTime.fromSeconds(
        stripeSubscription.current_period_start,
      )
        .setZone('utc')
        .toJSDate();

      this.logger.log('subscription.expiration_debug', {
        expireDate: newExpiration,
      });

      if (
        (subscription.expiresAt &&
          newExpiration.getTime() === subscription.expiresAt.getTime()) ||
        subscription.stripeLatestInvoice == stripeSubscription.latest_invoice
      ) {
        this.logger.log(
          'subscription.stripe.update_subscription_status_no_change',
          {
            newExpiration,
            currentExpiration: subscription.expiresAt,
            currentLatestInvoice: subscription.stripeLatestInvoice,
            newLatestInvoice: stripeSubscription.latest_invoice,
          },
        );

        return;
      }

      let isDowngrade = false;

      if (subscription.previousSubscriptionId) {
        const previousSubscription = await this.provider.get(
          subscription.previousSubscriptionId,
        );

        previousSubscription.status = SubscriptionStatusEnum.INACTIVE;

        await this.repository.save(previousSubscription);

        if (
          subscription.creditPackage.price <
          previousSubscription.creditPackage.price
        ) {
          isDowngrade = true;
        }
      }

      // Update subscription dates
      subscription.expiresAt = newExpiration;

      const isFirstActivation =
        subscription.status === SubscriptionStatusEnum.NEW ||
        subscription.status === SubscriptionStatusEnum.PENDING;

      if (isFirstActivation) {
        // First time activation - set paidAt
        subscription.paidAt = paidDate;
      } else {
        // Renewal - set renewedAt
        subscription.renewedAt = paidDate;
        subscription.paidAt = paidDate; // Always update paidAt with latest renewal date
      }

      subscription.externalReference = stripeSubscription.id as string;
      subscription.stripeLatestInvoice =
        stripeSubscription.latest_invoice as string;

      // Update status
      if (isFirstActivation) {
        subscription.status = SubscriptionStatusEnum.ACTIVE;
        subscription.stripeCheckoutUrl = null;
      }

      // Save the subscription with updated dates before processing credits
      await this.repository.save(subscription);

      // Handle first activation vs renewal differently
      if (
        stripeSubscription.current_period_start ==
          stripeSubscription.start_date ||
        isFirstActivation
      ) {
        // First activation or initial subscription
        await this.registerTopUpTransaction(subscription);
        await this.emailManager.sendPaidEmail(subscription);
      } else {
        // Handle renewal differently for yearly vs monthly subscriptions
        if (this.isYearlySubscription(subscription)) {
          await this.handleYearlySubscriptionRenewal(subscription);
        } else {
          await this.emailManager.sendRenewedEmail(subscription);
          await this.registerTopUpTransaction(subscription);
          await this.notifier.dispatch(
            new SubscriptionRenewedNotification(subscription.userId, {
              id: subscription.id,
              userId: subscription.userId,
              renewedAt: subscription.renewedAt,
            }),
          );
        }
      }
    } else if (stripeSubscription.status == 'past_due') {
      const stripeInvoice = await this.stripe.invoices.retrieve(
        stripeSubscription.latest_invoice as string,
      );

      subscription.stripeCheckoutUrl = stripeInvoice.hosted_invoice_url;
      await this.repository.save(subscription);
    } else if (stripeSubscription.status == 'canceled') {
      await this.cancelSubscription(subscription);

      await this.emailManager.sendCancellationEmail(subscription);
    }
  }

  async activateSubscription(
    subscription: SubscriptionEntity,
    topupAccount = true,
  ) {
    this.logger.log('subscription.activate', {
      subscriptionId: subscription.id,
      status: subscription.status,
      topupAccount: topupAccount,
    });

    // if subscription status is not NEW or PENDING
    if (
      SubscriptionStatusEnum.NEW != subscription.status &&
      SubscriptionStatusEnum.PENDING != subscription.status
    ) {
      return;
    }

    subscription.status = SubscriptionStatusEnum.ACTIVE;
    subscription.stripeCheckoutUrl = null;

    await this.repository.save(subscription);

    if (topupAccount) {
      await this.registerTopUpTransaction(subscription);
    }

    this.eventEmitter.emit(
      'subscription.activated',
      new SubscriptionActivatedEvent({
        id: subscription.id,
        userId: subscription.userId,
      }),
    );
  }

  async cancelSubscription(subscription: SubscriptionEntity) {
    switch (subscription.status) {
      case SubscriptionStatusEnum.NEW:
      case SubscriptionStatusEnum.PENDING:
        await this.repository.softDelete(subscription.id);

        if (subscription.stripeCheckoutSessionId) {
          try {
            await this.stripe.checkout.sessions.expire(
              subscription.stripeCheckoutSessionId,
            );
          } catch (e) {
            this.logger.log('Error expiring checkout session', {
              subscriptionId: subscription.id,
              stripeCheckoutSessionId: subscription.stripeCheckoutSessionId,
              error: {
                message: e?.message,
                stack: e?.stack,
                code: e?.code,
                type: e?.type,
                name: e?.name,
              },
            });
          }
        }

        if (
          subscription.paidAt === null &&
          subscription.previousSubscriptionId
        ) {
          const previousSubscription = await this.provider.get(
            subscription.previousSubscriptionId,
          );

          const stripeSubscription = await this.stripe.subscriptions.retrieve(
            previousSubscription.externalReference,
          );

          const stripePriceId =
            previousSubscription.creditPackage.stripePriceId;

          await this.sendStripeSubscriptionUpdate(
            previousSubscription.externalReference,
            {
              proration_behavior: 'none',
              items: [
                {
                  id: stripeSubscription.items.data[0].id,
                  price: stripePriceId,
                },
              ],
            },
          );
        }

        break;
      case SubscriptionStatusEnum.ACTIVE:
        const stripeSubscription = await this.stripe.subscriptions.retrieve(
          subscription.externalReference,
        );

        if (stripeSubscription.status != 'canceled') {
          await this.stripe.subscriptions.cancel(stripeSubscription.id);
        }

        subscription.status = SubscriptionStatusEnum.INACTIVE;

        await this.repository.save(subscription);
        break;
    }

    this.eventEmitter.emit(
      'subscription.canceled',
      new SubscriptionCanceledEvent({
        id: subscription.id,
        userId: subscription.userId,
      }),
    );
  }

  async registerTopUpTransaction(subscription: SubscriptionEntity) {
    const user = await this.userProvider.get(subscription.userId);
    const creditPackage = subscription.creditPackage;

    const transactionContext = {
      subscriptionId: subscription.id,
      userId: subscription.userId,
      creditPackageId: creditPackage.id,
      stripeInvoice: subscription.stripeLatestInvoice,
    };

    this.logger.log(
      'subscription.register_topup_transaction',
      transactionContext,
    );

    const refDate =
      subscription.renewedAt || subscription.paidAt || subscription.createdAt;
    const transactionReference = `${subscription.id}|${refDate.toISOString()}`;

    await this.transactionManager.register(
      TransactionTypeEnum.SUBSCRIPTION,
      creditPackage.price,
      creditPackage.name,
      transactionReference,
      false,
      user.id,
      null,
      transactionContext,
    );

    for (const creditType in creditPackage.creditTypes) {
      const amount = creditPackage.creditTypes[creditType];

      await this.transactionManager.register(
        TransactionTypeEnum.TOP_UP,
        amount,
        creditType,
        transactionReference,
        false,
        user.id,
        null,
        transactionContext,
      );

      // Calculate credit expiration date
      // For yearly subscriptions, credits expire monthly, not at subscription end
      const creditExpirationDate =
        this.calculateCreditExpirationDate(subscription);

      await this.userCreditBalanceManager.increase(
        creditType.toLowerCase() as CreditTypeEnum,
        amount,
        user.id,
        null,
        creditExpirationDate?.toJSDate(),
      );
    }
  }

  /**
   * Calculate credit expiration date based on subscription type
   * For yearly subscriptions, credits expire monthly
   * For monthly subscriptions, credits expire at subscription end
   */
  private calculateCreditExpirationDate(
    subscription: SubscriptionEntity,
  ): DateTime | null {
    const isYearlySubscription =
      subscription.creditPackage.expiresAfterMonths === 12;

    if (isYearlySubscription) {
      // For yearly subscriptions, credits expire monthly
      const baseDate =
        subscription.renewedAt || subscription.paidAt || subscription.createdAt;
      return DateTime?.fromJSDate(baseDate).plus({ months: 1 });
    } else {
      // For monthly subscriptions, credits expire at subscription end
      return subscription.getExpiresAt();
    }
  }

  async getOrCreateStripeCustomerId(user: UserEntity): Promise<string> {
    if (user.stripeCustomerId) {
      return user.stripeCustomerId;
    }

    const stripeCustomer = await this.stripe.customers.create({
      email: user.email,
    });

    user.stripeCustomerId = stripeCustomer.id;

    await this.userManager.update(user);

    return stripeCustomer.id;
  }

  async createFreeSubscription(user: UserEntity) {
    const subscription = new SubscriptionEntity();

    const creditPackage = await this.creditPackageProvider.getBy({
      name: 'Free',
      isVisible: false,
    });

    subscription.user = user;
    subscription.name = creditPackage.name;
    subscription.price = creditPackage.price;
    subscription.creditPackage = creditPackage;

    await this.create(subscription);

    await this.activateSubscription(subscription);
  }

  async refreshPendingSubscriptions() {
    const subscriptions = await this.provider.findPending(100);

    await Promise.all(
      subscriptions.map(async (subscription) => {
        if (
          subscription.status == 'pending' &&
          !subscription.previousSubscriptionId
        ) {
          await this.updateStripeCheckoutStatus(subscription);
        } else {
          await this.updateStripeSubscriptionStatus(subscription);
        }
      }),
    );
  }

  async createTrialSubscription(
    user: UserEntity,
    planName = 'Beginner',
    durationDays = 30,
  ): Promise<SubscriptionEntity> {
    this.logger.log('subscription.createTrial', {
      userId: user.id,
      planName,
      durationDays,
    });

    const creditPackage = await this.creditPackageProvider.getBy({
      name: planName,
    });

    const subscription = new SubscriptionEntity();
    subscription.user = user;
    subscription.name = creditPackage.name;
    subscription.price = 0; // Trial is free
    subscription.creditPackage = creditPackage;

    // Set expiration date to now + durationDays
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + durationDays);
    subscription.expiresAt = expiresAt;

    await this.create(subscription);
    await this.activateSubscription(subscription, false);

    return subscription;
  }

  async deactivateSubscription(
    subscription: SubscriptionEntity,
  ): Promise<void> {
    this.logger.log('subscription.deactivate', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
      status: subscription.status,
    });

    if (subscription.status !== SubscriptionStatusEnum.ACTIVE) {
      return;
    }

    subscription.status = SubscriptionStatusEnum.INACTIVE;
    await this.repository.save(subscription);

    this.eventEmitter.emit(
      'subscription.canceled',
      new SubscriptionCanceledEvent({
        id: subscription.id,
        userId: subscription.userId,
      }),
    );
  }

  /**
   * Helper method to check if a subscription is yearly
   */
  isYearlySubscription(subscription: SubscriptionEntity): boolean {
    return subscription.creditPackage.expiresAfterMonths === 12;
  }

  /**
   * Handle yearly subscription renewal with monthly credit expiration
   */
  async handleYearlySubscriptionRenewal(
    subscription: SubscriptionEntity,
  ): Promise<void> {
    this.logger.log('subscription.yearly_renewal', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
    });

    // For yearly subscriptions, send renewal email and register transaction
    await this.emailManager.sendRenewedEmail(subscription);
    await this.registerTopUpTransaction(subscription);
    await this.notifier.dispatch(
      new SubscriptionRenewedNotification(subscription.userId, {
        id: subscription.id,
        userId: subscription.userId,
        renewedAt: subscription.paidAt,
      }),
    );
  }

  /**
   * Handle grace period for yearly subscriptions
   */
  async handleGracePeriod(subscription: SubscriptionEntity): Promise<{
    inGracePeriod: boolean;
    daysRemaining?: number;
    gracePeriodEnds?: Date;
  }> {
    // Only yearly subscriptions have grace periods
    if (!this.isYearlySubscription(subscription)) {
      return { inGracePeriod: false };
    }

    // Only past due subscriptions can be in grace period
    if (subscription.status !== SubscriptionStatusEnum.PAST_DUE) {
      return { inGracePeriod: false };
    }

    const gracePeriodDays = 7; // 7-day grace period
    const paidAt = subscription.paidAt || subscription.createdAt;
    const gracePeriodStart = DateTime.fromJSDate(paidAt);
    const gracePeriodEnd = gracePeriodStart.plus({ days: gracePeriodDays });
    const now = DateTime.now();

    if (now <= gracePeriodEnd) {
      const daysRemaining = Math.ceil(gracePeriodEnd.diff(now, 'days').days);
      return {
        inGracePeriod: true,
        daysRemaining,
        gracePeriodEnds: gracePeriodEnd.toJSDate(),
      };
    }

    return { inGracePeriod: false };
  }

  /**
   * Retry payment for yearly subscription
   */
  async retryYearlyPayment(subscription: SubscriptionEntity): Promise<{
    success: boolean;
    error?: string;
  }> {
    if (!this.isYearlySubscription(subscription)) {
      throw new BadRequestException('subscription.not_yearly');
    }

    if (subscription.status !== SubscriptionStatusEnum.PAST_DUE) {
      throw new BadRequestException('subscription.not_past_due');
    }

    try {
      const stripeSubscription = await this.stripe.subscriptions.retrieve(
        subscription.externalReference,
      );

      if (stripeSubscription.status === 'active') {
        // Payment succeeded, update subscription
        subscription.status = SubscriptionStatusEnum.ACTIVE;
        subscription.renewedAt = new Date();
        await this.repository.save(subscription);

        this.logger.log('subscription.retry_payment_success', {
          subscriptionId: subscription.id,
          userId: subscription.userId,
        });

        return { success: true };
      } else if (stripeSubscription.status === 'past_due') {
        return {
          success: false,
          error: 'Payment still past due in Stripe',
        };
      } else {
        return {
          success: false,
          error: `Unexpected Stripe status: ${stripeSubscription.status}`,
        };
      }
    } catch (error) {
      this.logger.error('subscription.retry_payment_error', {
        subscriptionId: subscription.id,
        error: {
          message: error?.message,
          stack: error?.stack,
          code: error?.code,
          type: error?.type,
          name: error?.name,
        },
      });

      return {
        success: false,
        error: error.message || 'Unknown error',
      };
    }
  }

  /**
   * Handle yearly payment failure
   */
  async handleYearlyPaymentFailure(
    subscription: SubscriptionEntity,
    error: any,
  ): Promise<void> {
    if (!this.isYearlySubscription(subscription)) {
      throw new BadRequestException('subscription.not_yearly');
    }

    this.logger.error('subscription.yearly_payment_failed', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
      error: {
        message: error?.message,
        stack: error?.stack,
        code: error?.code,
        type: error?.type,
        name: error?.name,
      },
    });

    // Update subscription status to past due
    subscription.status = SubscriptionStatusEnum.PAST_DUE;
    await this.repository.save(subscription);

    try {
      // Send payment failure email
      await this.emailManager.sendPaymentFailedEmail(subscription, error);
    } catch (emailError) {
      this.logger.error('subscription.payment_failed_email_error', {
        subscriptionId: subscription.id,
        emailError: {
          message: emailError?.message,
          stack: emailError?.stack,
          code: emailError?.code,
          type: emailError?.type,
          name: emailError?.name,
        },
      });
    }

    this.logger.log('subscription.yearly_payment_failure_handled', {
      subscriptionId: subscription.id,
      userId: subscription.userId,
    });
  }

  /**
   * Process expired grace periods
   */
  async processExpiredGracePeriods(): Promise<void> {
    this.logger.log('subscription.process_expired_grace_periods_started');

    const pastDueSubscriptions = await this.provider.findBy(
      {
        status: SubscriptionStatusEnum.PAST_DUE,
        creditPackage: {
          expiresAfterMonths: 12,
        },
      },
      1,
      1000,
    );

    let processedCount = 0;
    let canceledCount = 0;

    for (const subscription of pastDueSubscriptions) {
      try {
        const gracePeriodInfo = await this.handleGracePeriod(subscription);

        if (!gracePeriodInfo.inGracePeriod) {
          // Grace period has expired, cancel the subscription
          await this.cancelSubscription(subscription);
          canceledCount++;

          this.logger.log('subscription.grace_period_expired_canceled', {
            subscriptionId: subscription.id,
            userId: subscription.userId,
          });
        }

        processedCount++;
      } catch (error) {
        this.logger.error('subscription.process_expired_grace_period_error', {
          subscriptionId: subscription.id,
          error: {
            message: error?.message,
            stack: error?.stack,
            code: error?.code,
            type: error?.type,
            name: error?.name,
          },
        });
      }
    }

    this.logger.log('subscription.process_expired_grace_periods_completed', {
      totalProcessed: processedCount,
      canceledCount,
    });
  }

  /**
   * Calculate refund amount for yearly subscription cancellation
   */
  calculateYearlyRefund(subscription: SubscriptionEntity): {
    totalMonths: number;
    unusedMonths: number;
    refundAmount: number;
    monthlyRate: number;
  } {
    if (!this.isYearlySubscription(subscription)) {
      return {
        totalMonths: 0,
        unusedMonths: 0,
        refundAmount: 0,
        monthlyRate: 0,
      };
    }

    const totalMonths = 12;
    const monthlyRate = subscription.creditPackage.price / totalMonths;

    if (!subscription.paidAt) {
      return {
        totalMonths,
        unusedMonths: 0,
        refundAmount: 0,
        monthlyRate,
      };
    }

    const paidDate = DateTime.fromJSDate(subscription.paidAt);
    const now = DateTime.now();
    const monthsUsed = Math.floor(now.diff(paidDate, 'months').months);
    const unusedMonths = Math.max(0, totalMonths - monthsUsed);
    const refundAmount = unusedMonths * monthlyRate;

    return {
      totalMonths,
      unusedMonths,
      refundAmount: Math.round(refundAmount * 100) / 100, // Round to 2 decimal places
      monthlyRate,
    };
  }
}
