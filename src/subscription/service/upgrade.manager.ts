import { BadRequestException, Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { Logger } from 'nestjs-pino';
import { UserEntity } from 'src/user/entity/user.entity';
import { SubscriptionUpgradePreviewDto } from '../dto/subscription-upgrade-preview.dto';
import { SubscriptionUpgradeRequest } from '../dto/subscription-upgrade.request';
import { SubscriptionEntity } from '../entity/subscription.entity';
import { CreditPackageProvider } from './credit-package.provider';
import { SubscriptionManager } from './manager';
import { SubscriptionProvider } from './provider';
import { StripeService } from './stripe.service';

@Injectable()
export class UpgradeManager {
  constructor(
    private readonly subscriptionProvider: SubscriptionProvider,
    private readonly creditPackageProvider: CreditPackageProvider,
    private readonly subscriptionManager: SubscriptionManager,
    private readonly stripeService: StripeService,
    private readonly logger: Logger,
  ) {}

  /**
   * Preview a subscription upgrade with proration details
   */
  async previewUpgrade(
    user: UserEntity,
    request: SubscriptionUpgradeRequest,
  ): Promise<SubscriptionUpgradePreviewDto> {
    this.logger.log('upgrade.preview', {
      userId: user.id,
      targetCreditPackageId: request.targetCreditPackageId,
    });

    // Get current active subscription
    const currentSubscription = await this.subscriptionProvider.findCurrentlyActiveSubscriptionForUser(user.id);
    if (!currentSubscription) {
      throw new BadRequestException('upgrade.no_active_subscription');
    }

    // Get target credit package
    const targetCreditPackage = await this.creditPackageProvider.get(request.targetCreditPackageId);
    if (!targetCreditPackage) {
      throw new BadRequestException('upgrade.invalid_target_package');
    }

    // Validate upgrade is allowed
    const upgradeValidation = this.validateUpgrade(currentSubscription, targetCreditPackage);
    if (!upgradeValidation.allowed) {
      return {
        currentSubscriptionId: currentSubscription.id,
        currentPackageName: currentSubscription.creditPackage.name,
        currentPrice: currentSubscription.creditPackage.price,
        targetPackageName: targetCreditPackage.name,
        targetPrice: targetCreditPackage.price,
        proratedAmount: 0,
        creditedAmount: 0,
        netAmount: 0,
        isYearlyUpgrade: false,
        nextBillingDate: new Date(),
        yearlySavings: 0,
        upgradeAllowed: false,
        upgradeBlockedReason: upgradeValidation.reason,
      };
    }

    // Calculate proration if this is a Stripe subscription
    let proratedAmount = 0;
    let creditedAmount = 0;
    let netAmount = targetCreditPackage.price;

    if (currentSubscription.externalReference) {
      try {
        const stripeSubscription = await this.stripeService.getStripeSubscription(
          currentSubscription.externalReference,
        );
        
        const targetStripePriceId = await this.stripeService.getOrCreateStripePriceId(targetCreditPackage);
        const proratedInvoice = await this.stripeService.calculateProration(
          stripeSubscription,
          targetStripePriceId,
        );

        proratedAmount = proratedInvoice.amount_due;
        netAmount = proratedAmount;

        // Calculate credited amount (rough estimate)
        const currentPeriodStart = DateTime.fromSeconds(stripeSubscription.current_period_start);
        const currentPeriodEnd = DateTime.fromSeconds(stripeSubscription.current_period_end);
        const now = DateTime.now();
        const remainingDays = currentPeriodEnd.diff(now, 'days').days;
        const totalDays = currentPeriodEnd.diff(currentPeriodStart, 'days').days;
        const remainingRatio = remainingDays / totalDays;
        creditedAmount = Math.round(currentSubscription.creditPackage.price * remainingRatio);
      } catch (error) {
        this.logger.error('upgrade.proration_calculation_failed', {
          error,
          subscriptionId: currentSubscription.id,
        });
        // Fall back to simple calculation
        netAmount = targetCreditPackage.price;
      }
    }

    // Calculate yearly savings for monthly-to-yearly upgrades
    const isYearlyUpgrade = this.isMonthlyToYearlyUpgrade(currentSubscription, targetCreditPackage);
    let yearlySavings = 0;
    if (isYearlyUpgrade) {
      const monthlyAnnualCost = currentSubscription.creditPackage.price * 12;
      yearlySavings = monthlyAnnualCost - targetCreditPackage.price;
    }

    // Calculate next billing date
    const nextBillingDate = isYearlyUpgrade 
      ? DateTime.now().plus({ years: 1 }).toJSDate()
      : DateTime.now().plus({ months: targetCreditPackage.expiresAfterMonths || 1 }).toJSDate();

    return {
      currentSubscriptionId: currentSubscription.id,
      currentPackageName: currentSubscription.creditPackage.name,
      currentPrice: currentSubscription.creditPackage.price,
      targetPackageName: targetCreditPackage.name,
      targetPrice: targetCreditPackage.price,
      proratedAmount,
      creditedAmount,
      netAmount,
      isYearlyUpgrade,
      nextBillingDate,
      yearlySavings,
      upgradeAllowed: true,
    };
  }

  /**
   * Execute a subscription upgrade
   */
  async executeUpgrade(
    user: UserEntity,
    request: SubscriptionUpgradeRequest,
  ): Promise<SubscriptionEntity> {
    this.logger.log('upgrade.execute', {
      userId: user.id,
      targetCreditPackageId: request.targetCreditPackageId,
      confirmProration: request.confirmProration,
    });

    // Get current active subscription
    const currentSubscription = await this.subscriptionProvider.findCurrentlyActiveSubscriptionForUser(user.id);
    if (!currentSubscription) {
      throw new BadRequestException('upgrade.no_active_subscription');
    }

    // Get target credit package
    const targetCreditPackage = await this.creditPackageProvider.get(request.targetCreditPackageId);
    if (!targetCreditPackage) {
      throw new BadRequestException('upgrade.invalid_target_package');
    }

    // Validate upgrade is allowed
    const upgradeValidation = this.validateUpgrade(currentSubscription, targetCreditPackage);
    if (!upgradeValidation.allowed) {
      throw new BadRequestException(upgradeValidation.reason || 'upgrade.not_allowed');
    }

    // For proration-required upgrades, ensure confirmation
    if (currentSubscription.externalReference && !request.confirmProration) {
      throw new BadRequestException('upgrade.proration_confirmation_required');
    }

    // Create new subscription entity
    const newSubscription = new SubscriptionEntity();
    newSubscription.user = user;
    newSubscription.name = targetCreditPackage.name;
    newSubscription.price = targetCreditPackage.price;
    newSubscription.creditPackage = targetCreditPackage;

    // Execute the upgrade through subscription manager
    const upgradedSubscription = await this.subscriptionManager.create(newSubscription);

    this.logger.log('upgrade.executed', {
      userId: user.id,
      oldSubscriptionId: currentSubscription.id,
      newSubscriptionId: upgradedSubscription.id,
      targetCreditPackageId: request.targetCreditPackageId,
    });

    return upgradedSubscription;
  }

  /**
   * Validate if an upgrade is allowed
   */
  private validateUpgrade(
    currentSubscription: SubscriptionEntity,
    targetCreditPackage: any,
  ): { allowed: boolean; reason?: string } {
    // Check if target package is for the same entity type
    if (currentSubscription.creditPackage.targetEntity !== targetCreditPackage.targetEntity) {
      return {
        allowed: false,
        reason: 'upgrade.mismatched_entity_type',
      };
    }

    // Check if target package is recurring (subscriptions only)
    if (!targetCreditPackage.isRecurring) {
      return {
        allowed: false,
        reason: 'upgrade.target_not_recurring',
      };
    }

    // Prevent downgrades (lower price)
    if (targetCreditPackage.price < currentSubscription.creditPackage.price) {
      return {
        allowed: false,
        reason: 'upgrade.downgrade_not_allowed',
      };
    }

    // Prevent "upgrading" to the same package
    if (targetCreditPackage.id === currentSubscription.creditPackageId) {
      return {
        allowed: false,
        reason: 'upgrade.same_package',
      };
    }

    // Special validation for yearly upgrades
    const isYearlyUpgrade = this.isMonthlyToYearlyUpgrade(currentSubscription, targetCreditPackage);
    if (isYearlyUpgrade) {
      // Ensure the yearly package has the same base features as monthly
      const monthlyVariant = targetCreditPackage.name.replace(' - Yearly', '');
      if (currentSubscription.creditPackage.name !== monthlyVariant) {
        return {
          allowed: false,
          reason: 'upgrade.yearly_upgrade_different_tier',
        };
      }
    }

    return { allowed: true };
  }

  /**
   * Check if this is a monthly-to-yearly upgrade
   */
  private isMonthlyToYearlyUpgrade(currentSubscription: SubscriptionEntity, targetCreditPackage: any): boolean {
    return (
      currentSubscription.creditPackage.expiresAfterMonths === 1 &&
      targetCreditPackage.expiresAfterMonths === 12
    );
  }

  /**
   * Get available upgrade options for a user
   */
  async getUpgradeOptions(user: UserEntity): Promise<any[]> {
    const currentSubscription = await this.subscriptionProvider.findCurrentlyActiveSubscriptionForUser(user.id);
    if (!currentSubscription) {
      return [];
    }

    // Get all packages for the same entity type
    const allPackages = await this.creditPackageProvider.findBy(
      {
        targetEntity: currentSubscription.creditPackage.targetEntity,
        isRecurring: true,
        isVisible: true,
      },
      1,
      100,
    );

    // Filter to only valid upgrade options
    return allPackages.filter((pkg) => {
      const validation = this.validateUpgrade(currentSubscription, pkg);
      return validation.allowed;
    });
  }
}
