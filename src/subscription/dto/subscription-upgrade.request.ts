import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

export class SubscriptionUpgradeRequest {
  @ApiProperty({
    description: 'ID of the target credit package to upgrade to',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  @IsUUID()
  @IsNotEmpty()
  targetCreditPackageId: string;

  @ApiProperty({
    description: 'Whether to confirm the upgrade with proration charges',
    example: true,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  confirmProration?: boolean = false;
}
