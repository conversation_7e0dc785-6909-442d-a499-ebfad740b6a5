import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AwsModule } from 'src/aws/aws.module';
import { CoreModule } from 'src/core/core.module';
import { jwtConfig } from '../auth/config/jwt.config';
import { NotificationModule } from '../notification/module';
import { OrganizationModule } from '../organization/organization.module';
import { UserModule } from '../user/user.module';
import { CreditPackageController } from './controller/credit-package.controller';
import { SubscriptionController as SubscriptionInternalController } from './controller/internal/subscription.controller';
import { InternalUserCreditBalanceController } from './controller/internal/user-credit-balance.controller';

import { StripeController } from './controller/stripe.controller';
import { SubscriptionController } from './controller/subscription.controller';
import { UserCreditBalanceController } from './controller/user-credit-balance.controller';
import { CreditPackageEntity } from './entity/credit-package.entity';
import { SubscriptionEntity } from './entity/subscription.entity';
import { TransactionEntity } from './entity/transaction.entity';
import { UserCreditBalanceEntity } from './entity/user-credit-balance.entity';
import { ImageGeneratedListener } from './listener/image-generated.listener';
import { ImageLikedListener } from './listener/image-liked.listener';
import { UserCreatedListener } from './listener/user-created.listener';
import { UserEmailValidatedListener } from './listener/user-email-validated.listener';
import { CreditPackageManager } from './service/credit-package.manager';
import { CreditPackageProvider } from './service/credit-package.provider';
import { CreditPackageResponseMapper } from './service/credit-package.response-mapper';

import { EmailManager } from './service/email.manager';
import { SubscriptionManager } from './service/manager';

import { PaymentInfoController } from './controller/payment-info.controller';
import { PaymentInfoProvider } from './service/payment-info.provider';
import { SubscriptionProvider } from './service/provider';
import { SubscriptionRequestManager } from './service/request-manager';
import { SubscriptionResponseMapper } from './service/response-mapper';
import { stripeFactory } from './service/stripe.factory';
import { StripeRequestManager } from './service/stripe.request-manager';
import { StripeService } from './service/stripe.service';
import { TransactionManager } from './service/transaction.manager';
import { UserCreditBalanceManager } from './service/user-credit-balance.manager';
import { UserCreditBalanceProvider } from './service/user-credit-balance.provider';
import { UserCreditBalanceRequestManager } from './service/user-credit-balance.request-manager';
import { UserCreditBalanceResponseMapper } from './service/user-credit-balance.response-mapper';

@Module({
  exports: [
    'Stripe',
    CreditPackageManager,
    CreditPackageProvider,
    StripeService,
    TransactionManager,
    UserCreditBalanceManager,
    UserCreditBalanceProvider,
  ],
  imports: [
    AwsModule,
    CoreModule,
    TypeOrmModule.forFeature([
      CreditPackageEntity,
      SubscriptionEntity,
      UserCreditBalanceEntity,
      TransactionEntity,
    ]),
    PassportModule,
    JwtModule.register(jwtConfig),
    forwardRef(() => NotificationModule),
    forwardRef(() => OrganizationModule),
    forwardRef(() => UserModule),
  ],
  providers: [
    CreditPackageManager,
    CreditPackageProvider,
    CreditPackageResponseMapper,
    EmailManager,
    ImageGeneratedListener,
    ImageLikedListener,
    stripeFactory,
    StripeRequestManager,
    StripeService,
    SubscriptionManager,
    SubscriptionProvider,
    SubscriptionRequestManager,
    SubscriptionResponseMapper,
    TransactionManager,
    UserCreatedListener,
    UserCreditBalanceManager,
    UserCreditBalanceProvider,
    UserCreditBalanceRequestManager,
    UserCreditBalanceResponseMapper,
    UserEmailValidatedListener,
    PaymentInfoProvider,
  ],
})
export class SubscriptionModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: SubscriptionModule,
      controllers: enableControllers
        ? [
            CreditPackageController,
            InternalUserCreditBalanceController,
            StripeController,
            SubscriptionController,
            SubscriptionInternalController,
            UserCreditBalanceController,
            PaymentInfoController,
          ]
        : [],
    };
  }
}
