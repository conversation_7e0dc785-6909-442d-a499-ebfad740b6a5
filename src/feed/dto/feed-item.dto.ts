import { ApiProperty } from '@nestjs/swagger';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';
import { VideoDto } from 'src/video/dto/video.dto';

export class FeedItemDto {
  @ApiProperty({ description: 'Type of feed item', enum: ['image', 'video'] })
  type: string;

  @ApiProperty({ description: 'Image completion data', required: false })
  imageCompletion?: ImageCompletionDto;

  @ApiProperty({ description: 'Video data', required: false })
  video?: VideoDto;
}
