import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ImageCompletionModule } from 'src/image-completion/module';
import { ModelModule } from 'src/model/module';
import { jwtConfig } from '../auth/config/jwt.config';
import { UserModule } from '../user/user.module';
import { ReadController } from './controller/read.controller';
import { FeedProvider } from './service/provider';
import { FeedRequestManager } from './service/request-manager';
import { FeedResponseMapper } from './service/response-mapper';
import { VideoModule } from 'src/video/module';

@Module({
  imports: [
    PassportModule,
    JwtModule.register(jwtConfig),
    UserModule,
    ImageCompletionModule,
    VideoModule,
    forwardRef(() => ModelModule),
  ],
  providers: [FeedResponseMapper, FeedRequestManager, FeedProvider],
})
export class FeedModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: FeedModule,
      controllers: enableControllers ? [ReadController] : [],
    };
  }
}
