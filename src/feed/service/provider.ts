import { Injectable } from '@nestjs/common';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionProvider } from 'src/image-completion/service/provider';
import { VideoEntity } from 'src/video/entity/video.entity';
import { VideoProvider } from 'src/video/service/provider';

export interface FeedItem {
  type: 'image' | 'video';
  entity: ImageCompletionEntity | VideoEntity;
  createdAt: Date;
}

@Injectable()
export class FeedProvider {
  constructor(
    private imageCompletionProvider: ImageCompletionProvider,
    private videoProvider: VideoProvider,
  ) {}

  async find(
    criteria: any,
    page: number,
    limit: number,
    sortBy?: string,
    sortOrder: 'ASC' | 'DESC' | 'RANDOM' = 'ASC',
  ): Promise<FeedItem[]> {
    // If showVideos is explicitly false, only return images
    if (criteria.showVideos === false) {
      const images = await this.imageCompletionProvider.findBy(
        criteria,
        page,
        limit,
        sortBy,
        sortOrder,
      );
      return images.map((image) => ({
        type: 'image' as const,
        entity: image,
        createdAt: image.createdAt,
      }));
    }

    // Fetch both images and videos
    const [images, videos] = await Promise.all([
      this.imageCompletionProvider.findBy(
        { ...criteria, showVideos: false }, // Exclude video-related images
        1,
        limit * 2, // Fetch more to ensure we have enough after merging
        sortBy,
        sortOrder,
      ),
      this.videoProvider.findBy(
        this.mapCriteriaForVideos(criteria),
        1,
        limit * 2, // Fetch more to ensure we have enough after merging
        sortBy,
        sortOrder,
      ),
    ]);

    // Combine and sort by creation date
    const feedItems: FeedItem[] = [
      ...images.map((image) => ({
        type: 'image' as const,
        entity: image,
        createdAt: image.createdAt,
      })),
      ...videos.map((video) => ({
        type: 'video' as const,
        entity: video,
        createdAt: video.createdAt,
      })),
    ];

    // Sort combined results
    if (sortOrder === 'RANDOM') {
      feedItems.sort(() => Math.random() - 0.5);
    } else {
      feedItems.sort((a, b) => {
        const comparison = a.createdAt.getTime() - b.createdAt.getTime();
        return sortOrder === 'ASC' ? comparison : -comparison;
      });
    }

    // Apply pagination to combined results
    const startIndex = (page - 1) * limit;
    return feedItems.slice(startIndex, startIndex + limit);
  }

  async count(criteria: any): Promise<number> {
    // If showVideos is explicitly false, only count images
    if (criteria.showVideos === false) {
      return await this.imageCompletionProvider.countBy(criteria);
    }

    // Count both images and videos
    const [imageCount, videoCount] = await Promise.all([
      this.imageCompletionProvider.countBy({ ...criteria, showVideos: false }),
      this.videoProvider.countBy(this.mapCriteriaForVideos(criteria)),
    ]);

    return imageCount + videoCount;
  }

  private mapCriteriaForVideos(criteria: any): any {
    const videoCriteria = { ...criteria };

    // Remove image-specific filters
    delete videoCriteria.showVideos;
    delete videoCriteria.editedImages;

    // Map common filters that apply to videos
    // Videos should have similar privacy/status concepts if needed
    if (videoCriteria.privacy) {
      // Videos don't have privacy field, so we'll filter by status instead
      delete videoCriteria.privacy;
    }

    return videoCriteria;
  }
}
