import { Injectable } from '@nestjs/common';
import { ImageCompletionEntity } from 'src/image-completion/entity/image-completion.entity';
import { ImageCompletionResponseMapper } from 'src/image-completion/service/response-mapper';
import { VideoEntity } from 'src/video/entity/video.entity';
import { VideoResponseMapper } from 'src/video/service/response-mapper';
import { FeedItemDto } from '../dto/feed-item.dto';
import { FeedItem } from './provider';

@Injectable()
export class FeedResponseMapper {
  constructor(
    private imageCompletionResponseMapper: ImageCompletionResponseMapper,
    private videoResponseMapper: VideoResponseMapper,
  ) {}

  async mapMultiple(
    feedItems: FeedItem[],
    userId: string = null,
  ): Promise<FeedItemDto[]> {
    const mappedItems = [];

    for (const feedItem of feedItems) {
      mappedItems.push(await this.mapFeedItem(feedItem, userId));
    }

    return mappedItems;
  }

  async mapFeedItem(
    feedItem: FeedItem,
    userId: string = null,
  ): Promise<FeedItemDto> {
    const dto = new FeedItemDto();
    dto.type = feedItem.type;

    if (feedItem.type === 'image') {
      dto.imageCompletion = await this.imageCompletionResponseMapper.map(
        feedItem.entity as ImageCompletionEntity,
        true,
        true,
        userId,
      );
    } else if (feedItem.type === 'video') {
      dto.video = await this.videoResponseMapper.map(
        feedItem.entity as VideoEntity,
        userId,
      );
    }

    return dto;
  }

  // Legacy method for backward compatibility
  async map(
    entity: ImageCompletionEntity,
    userId: string = null,
  ): Promise<FeedItemDto> {
    const dto = new FeedItemDto();

    dto.type = 'image';
    dto.imageCompletion = await this.imageCompletionResponseMapper.map(
      entity,
      true,
      true,
      userId,
    );

    return dto;
  }
}
