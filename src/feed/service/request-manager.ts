import { Injectable } from '@nestjs/common';
import {
  PrivacyEnum as ImageCompletionPrivacyEnum,
  StatusEnum as ImageCompletionStatusEnum,
} from 'src/image-completion/entity/image-completion.entity';
import { UserEntity } from 'src/user/entity/user.entity';
import { FeedSearchRequest } from '../dto/feed.search-request';
import { UserProvider } from 'src/user/service/provider';

@Injectable()
export class FeedRequestManager {
  constructor(private userProvider: UserProvider) {}

  async sanitizeSearchFilters(
    filters: FeedSearchRequest,
    currentUser: UserEntity,
  ): Promise<any> {
    if (filters.hasOwnProperty('username')) {
      const filteredUser = await this.userProvider.getBy({
        username: filters.username,
      });

      filters.userId = filteredUser.id;

      delete filters.username;
    }

    const criteria = {
      followerId: null,
      isNsfw: false,
      privacy: ImageCompletionPrivacyEnum.PUBLIC,
      status: ImageCompletionStatusEnum.READY,
      hideFromUserProfile: false,
      showVideos: filters.showVideos ?? true,
      ...filters,
    };

    if (filters.onlyFollowing) {
      criteria.followerId = currentUser.id;
    }

    return criteria;
  }
}
