import { DynamicModule, Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from 'src/auth/auth.module';
import { CoreModule } from 'src/core/core.module';
import { ImageEditModule } from 'src/image-edit/image-edit.module';
import { ModelModule } from 'src/model/module';
import { NotificationModule } from 'src/notification/module';
import { OrganizationModule } from 'src/organization/organization.module';
import { SubscriptionModule } from 'src/subscription/module';
import { UpscaleModule } from 'src/upscale/module';
import { jwtConfig } from '../auth/config/jwt.config';
import { BookmarkModule } from '../bookmark/module';
import { UserModule } from '../user/user.module';
import { CommentLikeController } from './controller/comment-like.controller';
import { CommentController } from './controller/comment.controller';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { ReadController as InternalReadController } from './controller/internal/read.controller';
import { UpdateController as InternalUpdateController } from './controller/internal/update.controller';
import { LikeController } from './controller/like.controller';
import { ReadController } from './controller/read.controller';
import { UpdateController } from './controller/update.controller';
import { ImageCompletionCommentLikeEntity } from './entity/image-completion-comment-like.entity';
import { ImageCompletionCommentEntity } from './entity/image-completion-comment.entity';
import { ImageCompletionLikeEntity } from './entity/image-completion-like.entity';
import { ImageCompletionModelEntity } from './entity/image-completion-model.entity';
import { ImageCompletionEntity } from './entity/image-completion.entity';
import { ImageDeletedListener } from './listener/image-deleted.listener';
import { AssetManager } from './service/asset.manager';
import { ImageCompletionCommentManager } from './service/comment.manager';
import { ImageCompletionCommentProvider } from './service/comment.provider';
import { ImageCompletionCommentRequestManager } from './service/comment.request-manager';
import { ImageCompletionCommentResponseMapper } from './service/comment.response-mapper';
import { ImageCompletionCommentLikeManager } from './service/image-completion-comment-like.manager';
import { ImageCompletionCommentLikeProvider } from './service/image-completion-comment-like.provider';
import { ImageCompletionCommentLikeRequestManager } from './service/image-completion-comment-like.request-manager';
import { ImageCompletionCommentLikeResponseMapper } from './service/image-completion-comment-like.response-mapper';
import { ImageCompletionLikeManager } from './service/like.manager';
import { ImageCompletionLikeProvider } from './service/like.provider';
import { ImageCompletionLikeRequestManager } from './service/like.request-manager';
import { ImageCompletionLikeResponseMapper } from './service/like.response-mapper';
import { ImageCompletionManager } from './service/manager';
import { PromptManager } from './service/prompt.manager';
import { ImageCompletionProvider } from './service/provider';
import { ImageCompletionRequestManager } from './service/request-manager';
import { ImageCompletionResponseMapper } from './service/response-mapper';
import { VideoModule } from 'src/video/module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ImageCompletionEntity,
      ImageCompletionCommentEntity,
      ImageCompletionLikeEntity,
      ImageCompletionModelEntity,
      ImageCompletionCommentLikeEntity,
    ]),
    CoreModule,
    PassportModule,
    JwtModule.register(jwtConfig),
    forwardRef(() => AuthModule),
    forwardRef(() => BookmarkModule),
    forwardRef(() => ImageEditModule),
    forwardRef(() => VideoModule),
    forwardRef(() => ModelModule),
    forwardRef(() => NotificationModule),
    forwardRef(() => OrganizationModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => UpscaleModule),
    forwardRef(() => UserModule),
  ],
  exports: [
    AssetManager,
    ImageCompletionProvider,
    ImageCompletionRequestManager,
    ImageCompletionResponseMapper,
    ImageCompletionManager,
    PromptManager,
  ],
  providers: [
    AssetManager,
    ImageCompletionResponseMapper,
    ImageCompletionProvider,
    ImageCompletionRequestManager,
    ImageCompletionManager,
    ImageCompletionCommentProvider,
    ImageCompletionCommentRequestManager,
    ImageCompletionCommentResponseMapper,
    ImageCompletionCommentManager,
    ImageCompletionLikeProvider,
    ImageCompletionLikeRequestManager,
    ImageCompletionLikeResponseMapper,
    ImageCompletionLikeManager,
    ImageCompletionCommentLikeProvider,
    ImageCompletionCommentLikeRequestManager,
    ImageCompletionCommentLikeResponseMapper,
    ImageCompletionCommentLikeManager,
    ImageDeletedListener,
    PromptManager,
  ],
})
export class ImageCompletionModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: ImageCompletionModule,
      controllers: enableControllers
        ? [
            CreateController,
            DeleteController,
            UpdateController,
            LikeController,
            CommentController,
            CommentLikeController,
            ReadController,
            InternalReadController,
            InternalUpdateController,
          ]
        : [],
    };
  }
}
