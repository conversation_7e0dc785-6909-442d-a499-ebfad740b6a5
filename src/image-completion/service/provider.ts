import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { LocalCacheService } from 'src/core/service/local-cache.service';
import {
  Brackets,
  EntityNotFoundError,
  FindOneOptions,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import {
  ImageCompletionEntity,
  PrivacyEnum,
} from '../entity/image-completion.entity';

@Injectable()
export class ImageCompletionProvider extends AbstractProvider<ImageCompletionEntity> {
  constructor(
    @InjectRepository(ImageCompletionEntity)
    repository: Repository<ImageCompletionEntity>,
    logger: Logger,
  ) {
    super(repository, logger);
  }

  async get(id: string): Promise<ImageCompletionEntity> {
    const imageCompletion = await this.createBaseQueryBuilder()
      .where('imageCompletion.id = :id', { id })
      .getOne();

    if (!imageCompletion) {
      throw new EntityNotFoundError(this.repository.target, id);
    }

    return imageCompletion;
  }

  createBaseQueryBuilder(): SelectQueryBuilder<ImageCompletionEntity> {
    return this.repository
      .createQueryBuilder('imageCompletion')
      .innerJoinAndSelect('imageCompletion.user', 'user')
      .leftJoinAndSelect(
        'imageCompletion.imageEditImageCompletion',
        'imageEditImageCompletion',
      )
      .leftJoinAndSelect(
        'imageCompletion.videoImageCompletion',
        'videoImageCompletion',
      )
      .leftJoinAndSelect('videoImageCompletion.video', 'video')
      .leftJoinAndSelect('imageCompletion.boards', 'boardImageCompletions')
      .leftJoinAndSelect('imageEditImageCompletion.imageEdit', 'imageEdit')
      .leftJoinAndSelect('imageCompletion.models', 'imageCompletionModel')
      .leftJoinAndSelect('imageCompletionModel.model', 'model')
      .leftJoinAndSelect('model.user', 'modelUser')
      .leftJoinAndSelect('imageCompletion.upscales', 'upscales');
  }

  prepareFindOneOptions(criteria: any): FindOneOptions {
    return {
      where: criteria,
      relations: {
        user: true,
        imageEditImageCompletion: {
          imageEdit: true,
        },
      },
    };
  }

  countMonthlyStatistics(year: number, month: number) {
    const start = `${year}-${String(month).padStart(2, '0')}-01`;
    return this.repository.query(`
      WITH days AS (
        SELECT generate_series(
          DATE '${start}',
          (DATE '${start}' + INTERVAL '1 month - 1 day'),
          INTERVAL '1 day'
        )::date AS day
      )
      SELECT
        EXTRACT(DAY FROM days.day)::int AS day,
        COALESCE(COUNT(ic.id), 0) AS amount
      FROM days
      LEFT JOIN image_completion ic
        ON DATE(ic.created_at) = days.day
      GROUP BY days.day
      ORDER BY days.day;
    `);
  }

  async countActiveUserStatistics(initialDate: Date, endDate: Date) {
    const result = await this.repository.query(`
      SELECT COUNT(DISTINCT user_id) AS amount FROM image_completion WHERE created_at BETWEEN '${initialDate.toISOString()}' AND '${endDate.toISOString()}';
      `);
    return parseInt(result[0].amount, 10);
  }

  async countImageStatistics(initialDate: Date, endDate: Date) {
    const result = await this.repository.query(`
      SELECT COUNT(*) AS amount FROM image_completion WHERE created_at BETWEEN '${initialDate.toISOString()}' AND '${endDate.toISOString()}';
      `);
    return parseInt(result[0].amount, 10);
  }

  countMonthlyActiveUsers(year: number, month: number) {
    const start = `${year}-${String(month).padStart(2, '0')}-01`;
    return this.repository.query(`
      WITH days AS (
        SELECT generate_series(
          DATE '${start}',
          (DATE '${start}' + INTERVAL '1 month - 1 day'),
          INTERVAL '1 day'
        )::date AS day
      )
      SELECT
        EXTRACT(DAY FROM days.day)::int AS day,
        COALESCE(COUNT(DISTINCT ic.user_id), 0) AS amount
      FROM days
      LEFT JOIN image_completion ic
        ON DATE(ic.created_at) = days.day
      GROUP BY days.day
      ORDER BY days.day;
    `);
  }

  countYearlyStatistics(year: number) {
    const start = `${year}-01-01`;
    const end = `${year + 1}-01-01`;
    return this.repository.query(
      `
    SELECT 
      EXTRACT(YEAR FROM created_at) AS year, 
      EXTRACT(MONTH FROM created_at) AS month,
      COUNT(*) AS amount 
    FROM image_completion 
    WHERE created_at >= '${start}' AND created_at < '${end}'
    GROUP BY year, month
    ORDER BY year, month;`,
    );
  }

  countYearlyActiveUsers(year: number) {
    const start = `${year}-01-01`;
    const end = `${year + 1}-01-01`;
    return this.repository.query(`
      SELECT 
        EXTRACT(YEAR FROM created_at) AS year, 
        EXTRACT(MONTH FROM created_at) AS month,
        COUNT(DISTINCT user_id) AS amount 
      FROM image_completion 
      WHERE created_at >= '${start}' AND created_at < '${end}'
      GROUP BY year, month
      ORDER BY year, month;`);
  }

  findLatestImagesByBoard(boardId: string, limit: number) {
    return this.repository.find({
      where: {
        boards: { boardId: boardId },
      },
      order: {
        boards: {
          createdAt: 'DESC',
        },
      },
      take: limit,
      relations: {
        user: true,
        boards: true,
      },
    });
  }

  async countRelatedImages(
    criteria: any,
    id: string,
    userId: string,
    originalId: string,
  ) {
    const queryBuilder = this.prepareQueryBuilderRelatedImages(
      criteria,
      id,
      userId,
      originalId,
    );
    return queryBuilder.getCount();
  }

  async findRelatedImages(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'ASC',
    id: string,
    userId: string,
    originalId: string,
  ): Promise<ImageCompletionEntity[]> {
    const queryBuilder = this.prepareQueryBuilderRelatedImages(
      criteria,
      id,
      userId,
      originalId,
    );

    queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`imageCompletion.${sortBy}`, sortOrder);

    return await queryBuilder.getMany();
  }

  prepareQueryBuilderRelatedImages(
    criteria: any,
    id: string,
    userId: string,
    originalId: string,
  ): SelectQueryBuilder<ImageCompletionEntity> {
    const where = { ...criteria };
    const queryBuilder = this.createBaseQueryBuilder();

    queryBuilder.where(
      new Brackets((queryBrackets) => {
        queryBrackets
          .where('imageCompletion.id = :id', { id: id })
          .orWhere('imageCompletion.regeneratedFromId = :regeneratedFromId', {
            regeneratedFromId: id,
          })
          .orWhere(
            'imageCompletion.originalImageCompletionId = :originalImageCompletionId',
            {
              originalImageCompletionId: id,
            },
          );
      }),
    );
    queryBuilder.andWhere('imageCompletion.id <> :originalId', {
      originalId: originalId,
    });
    queryBuilder.andWhere(
      new Brackets((queryBrackets) => {
        queryBrackets
          .where('imageCompletion.userId = :userId', { userId: userId })
          .orWhere('imageCompletion.privacy = :privacy', {
            privacy: PrivacyEnum.PUBLIC,
          });
      }),
    );
    Object.keys(where).forEach((key) => {
      queryBuilder.andWhere(`imageCompletion.${key} = :${key}`, {
        [key]: where[key],
      });
    });
    return queryBuilder;
  }

  async findBy(
    criteria: any,
    page: number,
    limit: number,
    sortBy = 'createdAt',
    sortOrder: 'ASC' | 'DESC' | 'RANDOM' = 'ASC',
  ): Promise<ImageCompletionEntity[]> {
    const queryBuilder = this.prepareQueryBuilder(criteria);

    queryBuilder.take(limit);
    queryBuilder.skip((page - 1) * limit);

    if (sortOrder === 'RANDOM') {
      queryBuilder.addSelect('RANDOM()', 'random_order');
      queryBuilder.orderBy('random_order', 'ASC');
    } else {
      queryBuilder.orderBy(`imageCompletion.${sortBy}`, sortOrder);
    }

    return await queryBuilder.getMany();
  }

  async countBy(criteria: any): Promise<number> {
    const cacheKey = `count:${JSON.stringify(criteria)}`;

    const cachedCount = LocalCacheService.getCache(cacheKey);
    if (cachedCount !== null) {
      return cachedCount;
    }

    const queryBuilder = this.prepareQueryBuilder(criteria);
    const count = await queryBuilder.getCount();

    LocalCacheService.setCache(cacheKey, count);

    return count;
  }

  prepareQueryBuilder(
    criteria: any,
  ): SelectQueryBuilder<ImageCompletionEntity> {
    const where = { ...criteria };
    const queryBuilder = this.createBaseQueryBuilder();

    if (where.currentUserId) {
      queryBuilder.where(
        new Brackets((queryBrackets) => {
          queryBrackets
            .where('imageCompletion.userId = :currentUserId', {
              currentUserId: where.currentUserId,
            })
            .orWhere('imageCompletion.privacy = :privacy', {
              privacy: PrivacyEnum.PUBLIC,
            });
        }),
      );
    }
    delete where.currentUserId;

    if (where.prompt) {
      queryBuilder.andWhere(
        new Brackets((queryBrackets) => {
          queryBrackets.where('imageCompletion.prompt ILIKE :prompt', {
            prompt: `%${where.prompt}%`,
          });
        }),
      );
      queryBuilder.andWhere(
        new Brackets((queryBrackets) => {
          queryBrackets.where('imageCompletion.hidePrompt = :hidePrompt', {
            hidePrompt: false,
          });
        }),
      );
      delete where.prompt;
    }

    // if (where.username) {
    //   queryBuilder.where(
    //     new Brackets((queryBrackets) => {
    //       queryBrackets.where('user.username = :username', {
    //         username: where.username,
    //       });
    //     }),
    //   );

    //   delete where.username;
    // }

    if (!where.includeNsfw) {
      where.isNsfw = false;
    }
    delete where.includeNsfw;

    if (where.boardId) {
      queryBuilder.andWhere('boardImageCompletions.boardId = :boardId', {
        boardId: where.boardId,
      });

      delete where.boardId;
    }

    // Exclude image completions that have related videos by default unless explicitly allowed
    if (where.showVideos == null) {
      queryBuilder.andWhere('video.id IS NULL');
    }

    // if (where.blockedAt == null) {
    //   queryBuilder.andWhere('imageCompletion.blockedAt IS NULL');

    //   delete where.blockedAt;
    // }

    if (where.modelIds && where.modelIds.length > 0) {
      queryBuilder.andWhere('imageCompletionModel.modelId IN (:...modelIds)', {
        modelIds: where.modelIds,
      });
      delete where.modelIds;
    }

    if (where.onlyFollowing && where.followerId) {
      queryBuilder.leftJoinAndSelect('user.followers', 'follower');
      queryBuilder.andWhere('follower.followerId = :followerId', {
        followerId: where.followerId,
      });
    }

    delete where.followerId;
    delete where.onlyFollowing;

    if (where.username) {
      queryBuilder.andWhere('user.username = :username', {
        username: where.username,
      });
      delete where.username;
    }

    const d = new Date();
    if (where.day) {
      d.setDate(d.getDate() - 1);
    }
    if (where.week) {
      d.setDate(d.getDate() - 7);
    }
    if (where.month) {
      d.setMonth(d.getMonth() - 1);
    }
    if (where.year) {
      d.setFullYear(d.getFullYear() - 1);
    }

    if (where.day || where.week || where.month || where.year) {
      queryBuilder.andWhere(
        'imageCompletion.createdAt BETWEEN :start AND :end',
        {
          start: d.toISOString(),
          end: new Date().toISOString(),
        },
      );
    }

    delete where.day;
    delete where.week;
    delete where.month;
    delete where.year;

    if (where.initialDate) {
      queryBuilder.andWhere(
        'imageCompletion.createdAt >= :start AND imageCompletion.createdAt <= :end',
        {
          start: where.initialDate,
          end: where.endDate,
        },
      );
    }

    delete where.initialDate;
    delete where.endDate;

    if (where.editedImages != undefined) {
      if (where.editedImages) {
        queryBuilder.andWhere('imageEdit.id IS NOT NULL');
        queryBuilder.andWhere('imageEdit.deleted_at IS NULL');
      } else {
        queryBuilder.andWhere('imageEdit.id IS NULL');
      }

      delete where.editedImages;
    }

    if (where.showVideos != undefined) {
      // When showVideos is false, exclude images that have related videos
      if (!where.showVideos) {
        queryBuilder.andWhere('video.id IS NULL');
      }
      // When showVideos is true, don't add any video-related filters (show all images)

      delete where.showVideos;
    }

    Object.keys(where).forEach((key) => {
      queryBuilder.andWhere(`imageCompletion.${key} = :${key}`, {
        [key]: where[key],
      });
    });

    return queryBuilder;
  }

  async findOrganizationDashboardSeats(
    page: number,
    limit: number,
    organizationId: string,
  ) {
    let query = this.prepareOrganizationDashboardSeatsQuery(organizationId);

    query += `
    ORDER BY COUNT(image_completion.id) DESC
      LIMIT ${limit}
      OFFSET ${(page - 1) * limit};
    `;
    return await this.repository.query(query);
  }

  async countOrganizationDashboardSeats(organizationId: string) {
    const query =
      'SELECT COUNT(*) AS totalCount FROM ( ' +
      this.prepareOrganizationDashboardSeatsQuery(organizationId) +
      ') x';
    const result = await this.repository.query(query);

    return result.length > 0 ? result[0].totalCount : 0;
  }

  prepareOrganizationDashboardSeatsQuery(organizationId: string) {
    return `
      SELECT image_completion.user_id AS user_id,
      COUNT(image_completion.id) AS images_count
      FROM image_completion
      WHERE image_completion.organization_id = '${organizationId}'
      GROUP BY image_completion.user_id
    `;
  }
}
