import { ApiProperty } from '@nestjs/swagger';
import { ImageEditDto } from 'src/image-edit/dto/image-edit.dto';
import { ModelDto } from 'src/model/dto/model.dto';
import { PublicUserDto } from 'src/user/dto/public.user.dto';
import { VideoDto } from 'src/video/dto/video.dto';

export class ImageCompletionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  user: PublicUserDto;

  @ApiProperty()
  userId?: string;

  @ApiProperty()
  regeneratedFromId?: string;

  @ApiProperty()
  originalImageCompletion?: ImageCompletionDto;

  @ApiProperty()
  imageEdit?: ImageEditDto;

  @ApiProperty()
  prompt: string;

  @ApiProperty()
  baseModel?: string;

  @ApiProperty()
  promptSystem?: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  statusDetail: string;

  @ApiProperty()
  progress: number;

  @ApiProperty()
  previewImage: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  queue: string;

  @ApiProperty()
  isUserVerified: boolean;

  @ApiProperty()
  isHot: boolean;

  @ApiProperty()
  privacy: string;

  @ApiProperty()
  likes: number;

  @ApiProperty()
  comments: number;

  @ApiProperty()
  liked: boolean;

  @ApiProperty()
  regenerations: number;

  @ApiProperty()
  reports: number;

  @ApiProperty()
  storageBucket?: string;

  @ApiProperty()
  storagePath?: string;

  @ApiProperty()
  generationSettings?: any;

  @ApiProperty()
  generationData?: string;

  @ApiProperty()
  generatedByUnit?: string;

  @ApiProperty()
  generationSeconds?: number;

  @ApiProperty()
  imagePaths?: any;

  @ApiProperty()
  imageVersions: { [key: string]: any };

  @ApiProperty()
  videoVersions?: { [key: string]: any };

  @ApiProperty()
  video?: VideoDto;

  @ApiProperty()
  systemVersion: number;

  @ApiProperty()
  hasWatermark?: boolean;

  @ApiProperty()
  isNsfw?: boolean;

  @ApiProperty()
  webhookUrl?: string;

  @ApiProperty()
  isBookmarked?: boolean;

  @ApiProperty()
  isUnsafe?: boolean;

  @ApiProperty()
  hidePrompt?: boolean;

  @ApiProperty()
  blockedAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty({ type: () => [ModelDto] })
  models?: ModelDto[];
}
