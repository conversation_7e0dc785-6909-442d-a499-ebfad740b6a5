import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { VideoEntity, StatusEnum } from '../entity/video.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { AssetManager } from './asset.manager';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { TransactionManager } from 'src/subscription/service/transaction.manager';
import { TransactionTypeEnum } from 'src/subscription/entity/transaction-type.enum';
import { CreditTypeEnum } from 'src/subscription/entity/credit-type.enum';
import { VideoResponseMapper } from './response-mapper';
import {
  ImageCompletionEntity,
  StatusEnum as ImageCompletionStatusEnum,
  PrivacyEnum as ImageCompletionPrivacyEnum,
} from '../../image-completion/entity/image-completion.entity';
import { ImageCompletionManager } from '../../image-completion/service/manager';
import { VideoImageCompletionEntity } from '../entity/video-image-completion.entity';
import { ImageCompletionProvider } from '../../image-completion/service/provider';
import { UserProvider } from '../../user/service/provider';

@Injectable()
export class VideoManager {
  private MAX_BILLED_SECONDS = 35;

  constructor(
    @InjectRepository(VideoEntity)
    private repository: Repository<VideoEntity>,
    @InjectRepository(VideoImageCompletionEntity)
    private videoImageCompletionRepository: Repository<VideoImageCompletionEntity>,
    private assetManager: AssetManager,
    private configService: ConfigService,
    private transactionManager: TransactionManager,
    @Inject('SQS')
    private sqs: SQSClient,
    private responseMapper: VideoResponseMapper,
    private imageCompletionManager: ImageCompletionManager,
    private imageCompletionProvider: ImageCompletionProvider,
    private userProvider: UserProvider,
  ) {}

  async create(entity: VideoEntity): Promise<VideoEntity> {
    entity.id = uuidv4();
    entity.promptSystem = await this.generatePromptSystem(entity);
    entity.generationSeconds = 1;

    this.assetManager.generateStoragePath(entity);

    await this.save(entity);

    if (!entity.user.isBot) {
      await this.registerTransaction(entity);
    }

    await this.generateImageCompletions(entity);
    await this.writeToQueue(entity);

    return entity;
  }

  async update(entity: VideoEntity): Promise<VideoEntity> {
    await this.save(entity);

    return entity;
  }

  async registerTransaction(entity: VideoEntity) {
    const billedSeconds = Math.min(
      this.MAX_BILLED_SECONDS,
      entity.generationSeconds,
    );
    await this.transactionManager.register(
      TransactionTypeEnum.SPENDING,
      billedSeconds,
      CreditTypeEnum.IMAGE,
      entity.id,
      true,
      entity.organizationId ? null : entity.userId,
      entity.organizationId,
    );
  }

  async writeToQueue(entity: VideoEntity) {
    const queueUrl = this.configService.get<string>('VIDEO_SQS_QUEUE_URL');

    const sendMessageCommand = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(
        await this.responseMapper.mapInternal(entity),
      ),
    });

    await this.sqs.send(sendMessageCommand);
  }

  async save(entity: VideoEntity) {
    if (!entity.storagePath) {
      this.assetManager.generateStoragePath(entity);
    }
    await this.repository.save(entity);
  }

  async generatePromptSystem(entity: VideoEntity): Promise<string> {
    let promptSystem = entity.prompt || '';

    // Add all settings properties to prompt system
    if (entity.settings && typeof entity.settings === 'object') {
      Object.entries(entity.settings).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          // Convert camelCase to readable format
          const readableKey = key.replace(/([A-Z])/g, ' $1').toLowerCase();
          promptSystem += ` ${readableKey}: ${value},`;
        }
      });
    }

    // Add resolution-specific animation prompts
    if (entity.resolution) {
      promptSystem += ` ${entity.resolution}p resolution,`;
    }

    // Add dimension-specific prompts for animation
    promptSystem += ` ${entity.width}x${entity.height} aspect ratio`;

    promptSystem += promptSystem.endsWith(',') ? '' : ',';

    return promptSystem;
  }

  async delete(entity: VideoEntity): Promise<void> {
    await this.repository.softDelete(entity.id);
  }

  async saveImageCompletion(
    video: VideoEntity,
    imageCompletion: ImageCompletionEntity,
  ) {
    imageCompletion.status = ImageCompletionStatusEnum.READY;
    await this.imageCompletionManager.save(imageCompletion);

    video.generatedImageCompletion = imageCompletion;
    video.generatedImageCompletionId = imageCompletion.id;
    video.status = StatusEnum.SAVED;

    return await this.update(video);
  }

  async interrupt(entity: VideoEntity): Promise<void> {
    if (entity.status != 'generating' && entity.status != 'new') {
      throw new BadRequestException('Video is not being generated');
    }

    entity.status = 'interrupted';
    await this.save(entity);
  }

  async generateImageCompletions(entity: VideoEntity) {
    let originalImageCompletion = null;

    if (entity.originalImageCompletionId) {
      originalImageCompletion =
        entity.originalImageCompletion ??
        (await this.imageCompletionProvider.get(
          entity.originalImageCompletionId,
        ));
    }

    const user = entity.user ?? (await this.userProvider.get(entity.userId));

    // Initialize imageCompletions array if not already set
    if (!entity.imageCompletions) {
      entity.imageCompletions = [];
    }

    for (let i = 0; i < entity.imageCompletionsCount; i++) {
      const imageCompletion = new ImageCompletionEntity();
      imageCompletion.prompt = entity.prompt;
      imageCompletion.promptSystem = entity.promptSystem;
      imageCompletion.generationSettings =
        originalImageCompletion?.generationSettings ?? {};
      imageCompletion.hasWatermark =
        originalImageCompletion?.hasWatermark ?? true;
      imageCompletion.systemVersion =
        originalImageCompletion?.systemVersion ?? user.systemVersion;
      imageCompletion.user = entity.user;
      imageCompletion.userId = entity.userId;
      imageCompletion.organization = entity.organization;
      imageCompletion.organizationId = entity.organizationId;
      imageCompletion.status = ImageCompletionStatusEnum.NEW;
      imageCompletion.privacy = ImageCompletionPrivacyEnum.PRIVATE;
      imageCompletion.queue = 'slow';
      imageCompletion.isBilled = false;
      imageCompletion.originalImageCompletionId =
        originalImageCompletion?.id ?? null;

      await this.imageCompletionManager.generateImageCompletionModels(
        imageCompletion,
      );
      await this.imageCompletionManager.save(imageCompletion);

      const videoImageCompletion = new VideoImageCompletionEntity();
      videoImageCompletion.video = entity;
      videoImageCompletion.videoId = entity.id;
      videoImageCompletion.imageCompletion = imageCompletion;
      videoImageCompletion.imageCompletionId = imageCompletion.id;

      await this.videoImageCompletionRepository.save(videoImageCompletion);
      entity.imageCompletions.push(videoImageCompletion);
    }
  }
}
