import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { AbstractProvider } from 'src/core/service/abstract.provider';
import { FindOneOptions, Repository, SelectQueryBuilder } from 'typeorm';
import { VideoEntity } from '../entity/video.entity';

@Injectable()
export class VideoProvider extends AbstractProvider<VideoEntity> {
  constructor(
    @InjectRepository(VideoEntity)
    protected readonly repository: Repository<VideoEntity>,
    protected readonly logger: Logger,
  ) {
    super(repository, logger);
  }

  createBaseQueryBuilder(): SelectQueryBuilder<VideoEntity> {
    return this.repository
      .createQueryBuilder('video')
      .innerJoinAndSelect('video.user', 'user')
      .leftJoinAndSelect('video.organization', 'organization')
      .leftJoinAndSelect(
        'video.originalImageCompletion',
        'originalImageCompletion',
      )
      .leftJoinAndSelect(
        'originalImageCompletion.user',
        'originalImageCompletionUser',
      );
  }

  prepareQueryBuilder(
    criteria: any,
    includeRelations = true,
  ): SelectQueryBuilder<VideoEntity> {
    const where = { ...criteria };
    const queryBuilder = includeRelations
      ? this.createBaseQueryBuilder()
      : this.repository.createQueryBuilder('video');

    // Specific known filters
    if (where.userId) {
      queryBuilder.andWhere('video.userId = :userId', {
        userId: where.userId,
      });
      delete where.userId;
    }

    if (where.organizationId) {
      queryBuilder.andWhere('video.organizationId = :organizationId', {
        organizationId: where.organizationId,
      });
      delete where.organizationId;
    }

    if (where.status) {
      queryBuilder.andWhere('video.status = :status', {
        status: where.status,
      });
      delete where.status;
    }

    if (where.hidePrompt !== undefined) {
      queryBuilder.andWhere('video.hidePrompt = :hidePrompt', {
        hidePrompt: where.hidePrompt,
      });
      delete where.hidePrompt;
    }

    // Date window filters similar to images (day/week/month/year)
    const d = new Date();
    let hasDateWindow = false;
    if (where.day) {
      d.setDate(d.getDate() - 1);
      hasDateWindow = true;
      delete where.day;
    }
    if (where.week) {
      d.setDate(d.getDate() - 7);
      hasDateWindow = true;
      delete where.week;
    }
    if (where.month) {
      d.setMonth(d.getMonth() - 1);
      hasDateWindow = true;
      delete where.month;
    }
    if (where.year) {
      d.setFullYear(d.getFullYear() - 1);
      hasDateWindow = true;
      delete where.year;
    }
    if (hasDateWindow) {
      queryBuilder.andWhere('video.createdAt BETWEEN :start AND :end', {
        start: d.toISOString(),
        end: new Date().toISOString(),
      });
    }

    // Whitelist for generic equality filters on video columns only
    const allowedGenericKeys = new Set([
      'id',
      'width',
      'height',
      'resolution',
      'systemVersion',
      'storageBucket',
      'storagePath',
      'generationSeconds',
      'inputImageUrl',
      'prompt',
      'promptSystem',
      // NOTE: createdAt/updatedAt filters are handled via date windows above
    ]);

    Object.keys(where).forEach((key) => {
      if (allowedGenericKeys.has(key)) {
        queryBuilder.andWhere(`video.${key} = :${key}`, {
          [key]: where[key],
        });
      }
      // Ignore unsupported keys silently to prevent query errors
    });

    return queryBuilder;
  }

  prepareFindOneOptions(criteria: any): FindOneOptions<VideoEntity> {
    return {
      where: criteria,
      relations: {
        user: true,
        organization: true,
        originalImageCompletion: {
          user: true,
        },
      },
    };
  }
}
