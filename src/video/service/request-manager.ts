import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { VideoManager } from './manager';
import { VideoRequest } from '../dto/video.request';
import { Logger } from 'nestjs-pino';
import { StatusEnum, VideoEntity } from '../entity/video.entity';
import { UserEntity } from 'src/user/entity/user.entity';
import { OrganizationUserProvider } from 'src/organization/service/organization-user.provider';
import { VideoInternalRequest } from '../dto/video.internal-request';
import { VideoPromptPrivacyRequest } from '../dto/video.prompt-privacy-request';
import { VideoProvider } from './provider';
import { ImageCompletionProvider } from '../../image-completion/service/provider';

@Injectable()
export class VideoRequestManager {
  constructor(
    private manager: VideoManager,
    private readonly organizationUserProvider: OrganizationUserProvider,
    private readonly logger: Logger,
    private readonly provider: VideoProvider,
    private readonly imageCompletionProvider: ImageCompletionProvider,
  ) {}

  async interrupt(entity: VideoEntity) {
    if (
      entity.status != StatusEnum.GENERATING &&
      entity.status != StatusEnum.NEW
    ) {
      throw new BadRequestException('Video is not being generated');
    }

    entity.status = StatusEnum.INTERRUPTED;

    try {
      await this.manager.save(entity);
    } catch (e) {
      this.logger.error('Error interrupting video generation', {
        video: entity,
        error: e.message,
      });

      throw new BadRequestException(e.message);
    }
  }

  async create(request: VideoRequest, user: UserEntity): Promise<VideoEntity> {
    if (!request.originalImageCompletionId && !request.imageUrl) {
      throw new BadRequestException(
        'Must provide either originalImageCompletionId or imageUrl',
      );
    }
    const entity = new VideoEntity();
    entity.userId = user.id;
    entity.user = user;
    entity.originalImageCompletionId = request.originalImageCompletionId;
    entity.inputImageUrl = request.imageUrl;
    entity.prompt = request.prompt;
    entity.webhookUrl = request.webhookUrl;
    entity.width = request.width;
    entity.height = request.height;
    entity.resolution = request.resolution;
    entity.imageCompletionsCount = request.imageCompletionsCount;
    entity.settings = request.settings;

    if (user.hidePrompt) {
      entity.hidePrompt = user.hidePrompt;
    }

    if (request.organizationId) {
      if (
        !(await this.organizationUserProvider.isMember(
          user.id,
          request.organizationId,
        ))
      ) {
        throw new UnauthorizedException();
      }
      entity.organizationId = request.organizationId;
    }

    return await this.manager.create(entity);
  }

  async updateInternal(
    entity: VideoEntity,
    request: VideoInternalRequest,
  ): Promise<VideoEntity> {
    this.mapInternalRequestData(entity, request);

    return await this.manager.update(entity);
  }

  mapInternalRequestData(
    entity: VideoEntity,
    request: VideoInternalRequest,
  ): void {
    entity.status = request.status ?? entity.status;
    entity.storageBucket = request.storageBucket ?? entity.storageBucket;
    entity.storagePath = request.storagePath ?? entity.storagePath;
    entity.generationSeconds =
      request.generationSeconds ?? entity.generationSeconds;
    entity.webhookUrl = request.webhookUrl ?? entity.webhookUrl;
    entity.hidePrompt = request.hidePrompt ?? entity.hidePrompt;
    entity.width = request.width ?? entity.width;
    entity.height = request.height ?? entity.height;
    entity.resolution = request.resolution ?? entity.resolution;
    entity.videoPaths = request.videoPaths ?? entity.videoPaths;
  }

  async updatePromptPrivacy(
    entity: VideoEntity,
    request: VideoPromptPrivacyRequest,
  ): Promise<VideoEntity> {
    entity.hidePrompt = request.hidePrompt;
    return await this.manager.update(entity);
  }

  async saveImageCompletion(
    id: string,
    imageCompletionId: string,
    user: UserEntity,
  ): Promise<void> {
    const entity = await this.provider.getBy({
      id: id,
      userId: user.id,
    });

    if (!entity) {
      throw new BadRequestException('Video not found');
    }

    if (!entity.hasImageCompletion(imageCompletionId)) {
      throw new BadRequestException(
        'Video does not have this image completion',
      );
    }

    const imageCompletion = await this.imageCompletionProvider.get(
      imageCompletionId,
    );

    if (!imageCompletion) {
      throw new BadRequestException('Image completion not found');
    }

    await this.manager.saveImageCompletion(entity, imageCompletion);
  }
}
