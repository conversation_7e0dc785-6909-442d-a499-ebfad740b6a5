import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { VideoProvider } from './service/provider';
import { VideoResponseMapper } from './service/response-mapper';
import { AssetManager } from './service/asset.manager';
import { VideoManager } from './service/manager';
import { VideoRequestManager } from './service/request-manager';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VideoEntity } from './entity/video.entity';
import { CoreModule } from 'src/core/core.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { jwtConfig } from 'src/auth/config/jwt.config';
import { AuthModule } from 'src/auth/auth.module';
import { ImageCompletionModule } from 'src/image-completion/module';
import { OrganizationModule } from 'src/organization/organization.module';
import { UserModule } from 'src/user/user.module';
import { NotificationModule } from 'src/notification/module';
import { ReadController } from './controller/read.controller';
import { ReadController as InternalReadController } from './controller/internal/read.controller';
import { CreateController } from './controller/create.controller';
import { DeleteController } from './controller/delete.controller';
import { SubscriptionModule } from 'src/subscription/module';
import { UpdateController } from './controller/update.controller';
import { UpdateController as InternalUpdateController } from './controller/internal/update.controller';
import { VideoLikeEntity } from './entity/video-like.entity';
import { VideoCommentEntity } from './entity/video-comment.entity';
import { VideoCommentLikeEntity } from './entity/video-comment-like.entity';
import { BookmarkModule } from 'src/bookmark/module';
import { VideoImageCompletionEntity } from './entity/video-image-completion.entity';

@Module({
  exports: [
    VideoProvider,
    VideoResponseMapper,
    AssetManager,
    VideoManager,
    VideoRequestManager,
  ],
  imports: [
    CoreModule,
    TypeOrmModule.forFeature([
      VideoEntity,
      VideoLikeEntity,
      VideoCommentEntity,
      VideoCommentLikeEntity,
      VideoImageCompletionEntity,
    ]),
    PassportModule,
    JwtModule.register(jwtConfig),
    forwardRef(() => AuthModule),
    forwardRef(() => ImageCompletionModule),
    forwardRef(() => OrganizationModule),
    forwardRef(() => UserModule),
    forwardRef(() => NotificationModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => BookmarkModule),
  ],
  providers: [
    VideoProvider,
    VideoResponseMapper,
    AssetManager,
    VideoManager,
    VideoRequestManager,
  ],
})
export class VideoModule {
  static register(enableControllers: boolean): DynamicModule {
    return {
      module: VideoModule,
      controllers: enableControllers
        ? [
            InternalReadController,
            InternalUpdateController,
            CreateController,
            DeleteController,
            UpdateController,
            ReadController,
          ]
        : [],
    };
  }
}
