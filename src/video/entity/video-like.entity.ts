import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { VideoEntity } from './video.entity';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('video_like')
export class VideoLikeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_like_video_id')
  videoId: string;

  @ManyToOne(() => VideoEntity)
  video: VideoEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_like_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
