import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { OrganizationEntity } from '../../organization/entity/organization.entity';
import { UserEntity } from '../../user/entity/user.entity';
import { VideoImageCompletionEntity } from './video-image-completion.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum StatusEnum {
  NEW = 'new',
  GENERATING = 'generating',
  READY = 'ready',
  SAVED = 'saved',
  FAILED = 'failed',
  INTERRUPTED = 'interrupted',
}

@Entity('video')
export class VideoEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_video_organization_id')
  organizationId: string;

  @ManyToOne(() => OrganizationEntity)
  organization: OrganizationEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_video_original_image_completion_id')
  originalImageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { eager: true, nullable: false })
  originalImageCompletion: ImageCompletionEntity;

  @Column({ type: 'uuid', nullable: true })
  @Index('idx_video_generated_image_completion_id')
  generatedImageCompletionId: string;

  @ManyToOne(() => ImageCompletionEntity, { eager: false, nullable: true })
  generatedImageCompletion: ImageCompletionEntity;

  @OneToMany(
    () => VideoImageCompletionEntity,
    (videoImageCompletion) => videoImageCompletion.video,
    { cascade: true, eager: true },
  )
  imageCompletions: VideoImageCompletionEntity[];

  @Column({ type: 'text', nullable: true })
  prompt?: string;

  @Column({ type: 'text', nullable: true })
  promptSystem?: string;

  @Column({ type: 'text', nullable: true })
  storageBucket?: string;

  @Column({ type: 'text', nullable: true })
  storagePath?: string;

  @Column({ nullable: false, default: 480 })
  resolution?: number;

  @Column({ nullable: false, default: 480 })
  width: number;

  @Column({ nullable: false, default: 480 })
  height: number;

  @Column({ type: 'json', nullable: true })
  settings?: any;

  @Column({ type: 'text', nullable: true })
  webhookUrl?: string;

  @Column({ type: 'text', nullable: true })
  inputImageUrl?: string;

  @Column({ nullable: false, default: StatusEnum.NEW })
  status: string;

  @Column({ nullable: false, default: 1 })
  systemVersion?: number;

  @Column({ nullable: false, default: 0 })
  generationSeconds: number;

  @Column({ type: 'json', nullable: true })
  videoPaths?: any;

  @Column({ type: 'int', nullable: false })
  imageCompletionsCount: number;

  @Column({ default: false })
  hidePrompt: boolean;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  @Index('idx_video_deleted_at')
  deletedAt?: Date;

  @CreateDateColumn()
  @Index('idx_video_created_at')
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  hasImageCompletion(imageCompletionId: string): boolean {
    return this.imageCompletions.some(
      (videoImageCompletion) =>
        videoImageCompletion.imageCompletionId === imageCompletionId,
    );
  }
}
