import {
  Column,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { ImageCompletionEntity } from '../../image-completion/entity/image-completion.entity';
import { VideoEntity } from './video.entity';

@Entity('video_image_completion')
export class VideoImageCompletionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => VideoEntity, { eager: false })
  video: VideoEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_image_completion_video_id')
  videoId: string;

  @Column({ name: 'image_completion_id' })
  // @Index('idx_video_image_completion_image_completion_id')
  imageCompletionId: string;

  @OneToOne(
    () => ImageCompletionEntity,
    (imageCompletion) => imageCompletion.videoImageCompletion,
  )
  @JoinColumn({ name: 'image_completion_id' })
  imageCompletion: ImageCompletionEntity;

  @Column({ default: false })
  isSaved: boolean;
}
