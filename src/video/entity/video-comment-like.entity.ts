import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { VideoCommentEntity } from './video-comment.entity';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('video_comment_like')
export class VideoCommentLikeEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => VideoCommentEntity)
  videoComment: VideoCommentEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_comment_like_video_comment_id')
  videoCommentId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_comment_like_user_id')
  userId: string;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
