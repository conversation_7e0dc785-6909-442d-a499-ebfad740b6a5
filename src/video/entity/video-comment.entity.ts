import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { VideoEntity } from './video.entity';
import { UserEntity } from '../../user/entity/user.entity';

@Entity('video_comment')
export class VideoCommentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_comment_video_id')
  videoId: string;

  @ManyToOne(() => VideoEntity)
  video: VideoEntity;

  @Column({ type: 'uuid', nullable: false })
  @Index('idx_video_comment_user_id')
  userId: string;

  @ManyToOne(() => UserEntity)
  user: UserEntity;

  @Column({ type: 'text' })
  comment: string;

  @Column({ nullable: false, default: 0 })
  likes: number;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;
}
