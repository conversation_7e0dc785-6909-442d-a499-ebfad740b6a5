import { ApiProperty } from '@nestjs/swagger';
import { ImageCompletionDto } from 'src/image-completion/dto/image-completion.dto';

export class VideoDto {
  @ApiProperty({ description: 'Unique identifier for the video animation' })
  id: string;

  @ApiProperty({
    description: 'Original image completion being animated',
    type: () => ImageCompletionDto,
  })
  originalImageCompletion: ImageCompletionDto;

  @ApiProperty({
    description: 'Generated image completion',
    type: () => ImageCompletionDto,
  })
  generatedImageCompletion: ImageCompletionDto;

  @ApiProperty({
    description: 'Generated image completion choices',
    type: () => Array<ImageCompletionDto>,
  })
  imageCompletionChoices: ImageCompletionDto[];

  @ApiProperty({ description: 'Video animation prompt' })
  prompt?: string;

  @ApiProperty({ description: 'System prompt for video generation' })
  promptSystem?: string;

  @ApiProperty({ description: 'Input image URL' })
  imageUrl?: string;

  @ApiProperty({ description: 'Video width in pixels' })
  width: number;

  @ApiProperty({ description: 'Video height in pixels' })
  height: number;

  @ApiProperty({ description: 'Video resolution' })
  resolution?: number;

  @ApiProperty({ description: 'Number of video completions to be generated' })
  imageCompletionsCount: number;

  @ApiProperty({ description: 'Video generation settings' })
  settings?: any;

  @ApiProperty({
    description: 'Status of the video animation process',
    required: false,
  })
  status: string;

  @ApiProperty({ description: 'System version used for generation' })
  systemVersion?: number;

  @ApiProperty({ description: 'Generation time in seconds' })
  generationSeconds: number;

  @ApiProperty({ description: 'Generated video file paths' })
  videoPaths?: any;

  @ApiProperty({ description: 'Generated video file versions' })
  videoVersions?: { [key: string]: any };

  @ApiProperty({ description: 'Storage bucket for video files' })
  storageBucket?: string;

  @ApiProperty({ description: 'Storage path for video files' })
  storagePath?: string;

  @ApiProperty({ description: 'Webhook URL for completion notification' })
  webhookUrl?: string;

  @ApiProperty({ description: 'Whether to hide the prompt from public view' })
  hidePrompt?: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;
}
