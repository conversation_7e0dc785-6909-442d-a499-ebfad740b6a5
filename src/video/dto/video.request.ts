import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  IsU<PERSON><PERSON>,
  Max,
  <PERSON>,
} from 'class-validator';

export class VideoRequest {
  @ApiProperty({ description: 'Original image completion ID', required: false })
  @IsOptional()
  @IsUUID()
  originalImageCompletionId?: string;

  @ApiProperty({ description: 'Image URL', required: false })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiProperty({
    type: 'number',
    description: 'Number of image completions to generate',
    required: false,
  })
  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(5)
  imageCompletionsCount? = 1;

  @ApiProperty()
  @IsUUID()
  @IsOptional()
  organizationId?: string;

  @ApiProperty({ description: 'Width of the edited image', required: false })
  @IsInt()
  @IsOptional()
  width?: number;

  @ApiProperty({ description: 'Height of the edited image', required: false })
  @IsInt()
  @IsOptional()
  height?: number;

  @ApiProperty({
    description: 'Resolution of the edited image',
    required: false,
  })
  @IsInt()
  @IsOptional()
  resolution?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty()
  @IsOptional()
  @IsUrl()
  webhookUrl?: string;

  @ApiProperty({ default: false })
  @IsBoolean()
  hidePrompt = false;

  @ApiProperty()
  @IsOptional()
  @IsObject()
  settings?: any;
}
