import {
  <PERSON>,
  Get,
  Param,
  ParseUUIDPipe,
  Query,
  Request,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from 'src/auth/service/jwt-auth.guard';
import { setPaginationHeaders } from 'src/core/utils/pagination';
import { VideoDto } from '../dto/video.dto';
import { VideoProvider } from '../service/provider';
import { VideoResponseMapper } from '../service/response-mapper';
import { VideoSearchRequest } from '../dto/video.search-request';

@ApiTags('videos')
@Controller('videos')
@ApiBearerAuth()
export class ReadController {
  constructor(
    private provider: VideoProvider,
    private responseMapper: VideoResponseMapper,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe())
  @ApiOperation({
    operationId: 'video_list',
    summary: 'List videos',
    description:
      'Retrieves a paginated list of videos for the authenticated user.\n\n' +
      'Optional Query Parameters:\n' +
      '- page: Page number (default: 1)\n' +
      '- limit: Items per page (1-50, default: 10)\n' +
      '- sortBy: Field to sort by\n' +
      '- sortOrder: "ASC" or "DESC"\n' +
      '- status: Filter by status\n' +
      '- isNsfw: Filter by NSFW content\n',
  })
  @ApiQuery({
    type: VideoSearchRequest,
    description: 'Query parameters for searching and paginating videos',
  })
  @ApiOkResponse({
    type: [VideoDto],
    description: 'Paginated list of videos',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid query parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async find(
    @Request() request,
    @Query() query: VideoSearchRequest,
    @Res() res: Response,
  ): Promise<void> {
    const { page, limit, sortBy, sortOrder, ...inputFilters } = query;
    const filters = {
      ...inputFilters,
      userId: request.user.id,
    };

    const entities = await this.provider.findBy(
      filters,
      page,
      limit,
      sortBy,
      sortOrder,
    );

    const totalCount = await this.provider.countBy(filters);

    setPaginationHeaders(res, totalCount, page, limit);

    res.send(await this.responseMapper.mapMultiple(entities));
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    operationId: 'video_get',
    summary: 'Get video by ID',
    description:
      'Retrieves the details of a specific video by its unique identifier.',
  })
  @ApiParam({
    name: 'id',
    description: 'The unique identifier of the video',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    type: VideoDto,
    description: 'Video details',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request. Invalid video ID',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized. Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found. The video could not be found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error. Unexpected error occurred',
  })
  async get(
    @Request() request,
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<VideoDto> {
    const video = await this.provider.getBy({
      id,
      userId: request.user.id,
    });

    return this.responseMapper.map(video);
  }
}
