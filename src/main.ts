import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import {
    DocumentBuilder,
    SwaggerDocumentOptions,
    SwaggerModule,
} from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import * as basicAuth from 'express-basic-auth';
import { Logger } from 'nestjs-pino';
import { AppModule } from './app.module';
import { EntityNotFoundErrorFilter } from './core/typeorm/entity-not-found-error.filter';
import { QueryFailedErrorFilter } from './core/typeorm/query-failed-error.filter';
import { CustomValidationPipe } from './core/validation/custom-validation.pipe';

async function bootstrap() {
  if (process.env.NODE_ENV !== 'development') {
    const Sentry = await import('@sentry/nestjs');
    const { nodeProfilingIntegration } = await import('@sentry/profiling-node');

    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      integrations: [nodeProfilingIntegration()],
      // Tracing
      tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE), //  Capture 100% of the transactions

      // Set sampling rate for profiling - this is relative to tracesSampleRate
      profilesSampleRate: parseFloat(process.env.SENTRY_PROFILES_SAMPLE_RATE),
    });
  }

  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    cors: {
      origin: process.env.CORS_ALLOW_ORIGIN,
      credentials: true,
      allowedHeaders: '*',
      exposedHeaders: '*',
    },
    logger: ['error', 'warn', 'debug', 'log'],
    rawBody: true,
  });

  // Configure body parser limits AFTER creating the app
  app.use(bodyParser.json({
    limit: '10mb',
    verify: (req: any, res, buf) => { req.rawBody = buf.toString(); },
  }));
  app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));

  if (process.env.CONTROLLERS_ENABLE_PRIVATE === 'true') {
    app.use(
      '/doc*',
      basicAuth({
        challenge: true,
        users: {
          letzai: 'everything1337',
          dev: 'd3v@l3tzai',
        },
      }),
    );
  }

  app.enableCors();

  const { httpAdapter } = app.get(HttpAdapterHost);
  app.getHttpAdapter().getInstance().disable('x-powered-by');
  app.useLogger(app.get(Logger));
  app.useGlobalFilters(new QueryFailedErrorFilter(httpAdapter));
  app.useGlobalFilters(new EntityNotFoundErrorFilter(httpAdapter));
  app.useGlobalPipes(
    new CustomValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  );

  const swaggerConfig = new DocumentBuilder()
    .setTitle('LetzAI API')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description:
          'Authentication via Bearer token: provide a user integration token or an organization integration token.',
      },
      'Bearer',
    )
    .addServer(process.env.HTTP_HOST ?? 'https://api.letz.ai')
    .build();

  const swaggerOptions: SwaggerDocumentOptions = {
    operationIdFactory: (controllerKey: string, methodKey: string) =>
      controllerKey + '.' + methodKey,
  };

  const document = SwaggerModule.createDocument(
    app,
    swaggerConfig,
    swaggerOptions,
  );
  SwaggerModule.setup('doc', app, document);

  await app.listen(process.env.PORT);
}

bootstrap();
